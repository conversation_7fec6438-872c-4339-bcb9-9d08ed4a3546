#!/usr/bin/env python3
"""
Celery Tasks for Udemy Course Scraper Automation
================================================

This module contains all Celery tasks for automated scraper execution,
monitoring, health checks, and maintenance operations.
"""

import os
import sys
import json
import subprocess
import logging
import asyncio
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, Optional

from celery import current_task
from celery.exceptions import Retry, WorkerLostError
from celery_app import app, get_ist_timestamp, validate_database_connection, SCRAPER_SCRIPT_PATH, PROJECT_ROOT, safe_log

logger = logging.getLogger(__name__)

@app.task(bind=True, autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 300})
def run_udemy_scraper(self):
    """
    Main task to execute the Udemy course scraper.
    
    This task:
    1. Validates database connection
    2. Executes the enhanced scraper script
    3. Monitors execution and collects metrics
    4. Handles errors and retries
    5. Sends alerts on failure
    """
    task_id = self.request.id
    start_time = datetime.now()
    
    logger.info(f"[TASK] Starting Udemy scraper task [ID: {task_id}]")
    logger.info(f"[TIME] Started at: {get_ist_timestamp()}")
    
    try:
        # Update task state
        self.update_state(
            state='PROGRESS',
            meta={'status': 'Validating database connection', 'progress': 10}
        )
        
        # Validate database connection
        if not validate_database_connection():
            raise Exception("Database connection validation failed")
        
        # Update task state
        self.update_state(
            state='PROGRESS',
            meta={'status': 'Starting scraper execution', 'progress': 20}
        )
        
        # Execute the scraper script
        logger.info(f"[EXEC] Executing scraper: {SCRAPER_SCRIPT_PATH}")
        logger.info(f"[DIR] Working directory: {PROJECT_ROOT}")
        
        # Run the scraper with proper environment
        process = subprocess.Popen(
            [sys.executable, SCRAPER_SCRIPT_PATH],
            cwd=PROJECT_ROOT,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            encoding='utf-8',
            errors='replace'
        )
        
        # Monitor execution progress
        output_lines = []
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                line = output.strip()
                output_lines.append(line)
                logger.info(f"SCRAPER: {line}")
                
                # Update progress based on scraper output
                if "Found" in line and "courses that need scraping" in line:
                    self.update_state(
                        state='PROGRESS',
                        meta={'status': 'Courses identified for scraping', 'progress': 30}
                    )
                elif "Starting to scrape" in line:
                    self.update_state(
                        state='PROGRESS',
                        meta={'status': 'Scraping in progress', 'progress': 50}
                    )
                elif "Scraping completed" in line:
                    self.update_state(
                        state='PROGRESS',
                        meta={'status': 'Scraping completed, finalizing', 'progress': 90}
                    )
        
        # Get return code
        return_code = process.poll()
        end_time = datetime.now()
        execution_time = (end_time - start_time).total_seconds()
        
        if return_code == 0:
            # Success - collect and return metrics
            stats = collect_scraper_stats()
            
            result = {
                'status': 'SUCCESS',
                'task_id': task_id,
                'execution_time': execution_time,
                'started_at': start_time.isoformat(),
                'completed_at': end_time.isoformat(),
                'completed_at_ist': get_ist_timestamp(),
                'return_code': return_code,
                'scraper_stats': stats,
                'output_lines': len(output_lines)
            }
            
            logger.info(f"[SUCCESS] Scraper task completed successfully [ID: {task_id}]")
            logger.info(f"[TIME] Execution time: {execution_time:.2f} seconds")
            
            # Save execution report
            save_execution_report(result)
            
            return result
        else:
            # Failure
            error_msg = f"Scraper failed with return code: {return_code}"
            logger.error(f"[ERROR] {error_msg} [ID: {task_id}]")
            
            # Save error report
            error_result = {
                'status': 'FAILED',
                'task_id': task_id,
                'execution_time': execution_time,
                'started_at': start_time.isoformat(),
                'failed_at': end_time.isoformat(),
                'failed_at_ist': get_ist_timestamp(),
                'return_code': return_code,
                'error': error_msg,
                'output_lines': output_lines[-50:] if output_lines else []  # Last 50 lines
            }
            
            save_execution_report(error_result)
            
            # Send alert for failure
            send_failure_alert(error_result)
            
            raise Exception(error_msg)
            
    except Exception as e:
        logger.error(f"[CRITICAL] Critical error in scraper task [ID: {task_id}]: {str(e)}")
        
        # Send alert for critical failure
        error_result = {
            'status': 'CRITICAL_FAILURE',
            'task_id': task_id,
            'error': str(e),
            'failed_at_ist': get_ist_timestamp()
        }
        
        save_execution_report(error_result)
        send_failure_alert(error_result)
        
        # Re-raise for Celery retry mechanism
        raise

@app.task(bind=True)
def health_check(self):
    """
    Daily health check task to verify system status.
    """
    logger.info("[HEALTH] Running daily health check")
    
    health_status = {
        'timestamp': get_ist_timestamp(),
        'database_connection': False,
        'scraper_script_exists': False,
        'recent_execution_status': None,
        'disk_space_available': False,
        'overall_status': 'UNKNOWN'
    }
    
    try:
        # Check database connection
        health_status['database_connection'] = validate_database_connection()
        
        # Check scraper script exists
        health_status['scraper_script_exists'] = os.path.exists(SCRAPER_SCRIPT_PATH)
        
        # Check recent execution status
        recent_report = get_latest_execution_report()
        if recent_report:
            health_status['recent_execution_status'] = recent_report.get('status')
        
        # Check disk space (basic check)
        try:
            import shutil
            total, used, free = shutil.disk_usage(PROJECT_ROOT)
            free_gb = free // (1024**3)
            health_status['disk_space_available'] = free_gb > 1  # At least 1GB free
            health_status['free_space_gb'] = free_gb
        except:
            health_status['disk_space_available'] = True  # Assume OK if can't check
        
        # Determine overall status
        if all([
            health_status['database_connection'],
            health_status['scraper_script_exists'],
            health_status['disk_space_available']
        ]):
            health_status['overall_status'] = 'HEALTHY'
        else:
            health_status['overall_status'] = 'UNHEALTHY'
        
        logger.info(f"🏥 Health check completed: {health_status['overall_status']}")
        
        # Save health check report
        save_health_report(health_status)
        
        # Send alert if unhealthy
        if health_status['overall_status'] == 'UNHEALTHY':
            send_health_alert(health_status)
        
        return health_status
        
    except Exception as e:
        logger.error(f"❌ Health check failed: {str(e)}")
        health_status['overall_status'] = 'ERROR'
        health_status['error'] = str(e)
        return health_status

@app.task
def cleanup_old_logs():
    """
    Weekly cleanup task to remove old log files and reports.
    """
    logger.info("[CLEANUP] Running weekly log cleanup")
    
    try:
        cleanup_stats = {
            'timestamp': get_ist_timestamp(),
            'files_removed': 0,
            'space_freed_mb': 0
        }
        
        # Define cleanup patterns and age limits
        cleanup_patterns = [
            ('celery_*.log', 30),  # Celery logs older than 30 days
            ('execution_report_*.json', 90),  # Execution reports older than 90 days
            ('health_report_*.json', 30),  # Health reports older than 30 days
            ('udemy_scraper_enhanced.log.*', 30),  # Rotated scraper logs
        ]
        
        cutoff_date = datetime.now() - timedelta(days=30)
        
        for pattern, days in cleanup_patterns:
            for file_path in Path(PROJECT_ROOT).glob(pattern):
                if file_path.is_file():
                    file_age = datetime.fromtimestamp(file_path.stat().st_mtime)
                    if file_age < cutoff_date:
                        file_size = file_path.stat().st_size
                        file_path.unlink()
                        cleanup_stats['files_removed'] += 1
                        cleanup_stats['space_freed_mb'] += file_size / (1024 * 1024)
        
        logger.info(f"🧹 Cleanup completed: {cleanup_stats['files_removed']} files removed, "
                   f"{cleanup_stats['space_freed_mb']:.2f} MB freed")
        
        return cleanup_stats
        
    except Exception as e:
        logger.error(f"❌ Cleanup failed: {str(e)}")
        return {'error': str(e), 'timestamp': get_ist_timestamp()}

def collect_scraper_stats() -> Optional[Dict[str, Any]]:
    """Collect statistics from the scraper execution."""
    try:
        stats_file = Path(PROJECT_ROOT) / 'backend' / 'scraper_stats.json'
        if stats_file.exists():
            with open(stats_file, 'r') as f:
                return json.load(f)
    except Exception as e:
        logger.warning(f"Could not collect scraper stats: {e}")
    return None

def save_execution_report(report: Dict[str, Any]):
    """Save execution report to file."""
    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = Path(PROJECT_ROOT) / f'execution_report_{timestamp}.json'
        
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        logger.info(f"📊 Execution report saved: {report_file}")
        
    except Exception as e:
        logger.error(f"Failed to save execution report: {e}")

def save_health_report(health_status: Dict[str, Any]):
    """Save health check report to file."""
    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = Path(PROJECT_ROOT) / f'health_report_{timestamp}.json'
        
        with open(report_file, 'w') as f:
            json.dump(health_status, f, indent=2, default=str)
        
    except Exception as e:
        logger.error(f"Failed to save health report: {e}")

def get_latest_execution_report() -> Optional[Dict[str, Any]]:
    """Get the latest execution report."""
    try:
        report_files = list(Path(PROJECT_ROOT).glob('execution_report_*.json'))
        if report_files:
            latest_file = max(report_files, key=lambda x: x.stat().st_mtime)
            with open(latest_file, 'r') as f:
                return json.load(f)
    except Exception as e:
        logger.warning(f"Could not get latest execution report: {e}")
    return None

def send_failure_alert(error_result: Dict[str, Any]):
    """Send alert for scraper failure."""
    try:
        # This is a placeholder for alert implementation
        # You can integrate with email, Slack, Discord, etc.
        alert_message = f"""
🚨 UDEMY SCRAPER FAILURE ALERT 🚨

Task ID: {error_result.get('task_id', 'Unknown')}
Status: {error_result.get('status', 'Unknown')}
Time: {error_result.get('failed_at_ist', 'Unknown')}
Error: {error_result.get('error', 'Unknown error')}

Please check the logs and take appropriate action.
        """.strip()
        
        logger.error(alert_message)
        
        # TODO: Implement actual alerting mechanism
        # - Email notifications
        # - Slack/Discord webhooks
        # - SMS alerts
        # - Dashboard notifications
        
    except Exception as e:
        logger.error(f"Failed to send failure alert: {e}")

def send_health_alert(health_status: Dict[str, Any]):
    """Send alert for health check failure."""
    try:
        alert_message = f"""
⚠️ SYSTEM HEALTH ALERT ⚠️

Overall Status: {health_status.get('overall_status', 'Unknown')}
Time: {health_status.get('timestamp', 'Unknown')}

Status Details:
- Database Connection: {health_status.get('database_connection', 'Unknown')}
- Scraper Script: {health_status.get('scraper_script_exists', 'Unknown')}
- Disk Space: {health_status.get('disk_space_available', 'Unknown')}

Please investigate and resolve any issues.
        """.strip()
        
        logger.warning(alert_message)
        
    except Exception as e:
        logger.error(f"Failed to send health alert: {e}")
