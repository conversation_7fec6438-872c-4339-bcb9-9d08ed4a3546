#!/usr/bin/env python3
"""
Celery Management Script for Udemy Scraper Automation
=====================================================

This script provides easy management of Celery workers and beat scheduler
for the Udemy course scraper automation system.

Usage:
    python manage_celery.py start-worker    # Start Celery worker
    python manage_celery.py start-beat      # Start Celery beat scheduler
    python manage_celery.py start-all       # Start both worker and beat
    python manage_celery.py stop-all        # Stop all Celery processes
    python manage_celery.py status          # Show status of Celery processes
    python manage_celery.py test-task       # Run a test scraper task
    python manage_celery.py monitor         # Start Celery monitoring
"""

import os
import sys
import time
import signal
import subprocess
import argparse
import json
from datetime import datetime
from pathlib import Path

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def get_ist_timestamp():
    """Get current timestamp in IST format."""
    from datetime import timezone, timedelta
    ist_tz = timezone(timedelta(hours=5, minutes=30))
    return datetime.now(ist_tz).strftime("%Y-%m-%d %H:%M:%S IST")

def print_header(title):
    """Print formatted header."""
    print("=" * 80)
    safe_print(title, "🚀", "[SYSTEM]")
    print("=" * 80)
    safe_print(get_ist_timestamp(), "📅", "[TIME]")
    print("=" * 80)

def safe_print(message, emoji="", fallback_prefix=""):
    """Print message safely handling Unicode issues on Windows."""
    import platform

    if platform.system() == 'Windows':
        # Use fallback text for Windows
        if fallback_prefix:
            safe_message = f"{fallback_prefix} {message}"
        else:
            safe_message = message
    else:
        # Use emoji for Unix/Linux
        safe_message = f"{emoji} {message}" if emoji else message

    try:
        print(safe_message)
    except UnicodeEncodeError:
        # Final fallback - remove any problematic characters
        safe_message = message.encode('ascii', 'ignore').decode('ascii')
        print(f"[TEXT] {safe_message}")

def print_status(message, status_type="INFO"):
    """Print formatted status message."""
    icons = {
        "INFO": ("ℹ️", "[INFO]"),
        "SUCCESS": ("✅", "[SUCCESS]"),
        "WARNING": ("⚠️", "[WARNING]"),
        "ERROR": ("❌", "[ERROR]"),
        "PROCESSING": ("🔄", "[PROCESSING]")
    }
    emoji, fallback = icons.get(status_type, ("📝", "[NOTE]"))
    timestamp = datetime.now().strftime('%H:%M:%S')
    safe_print(f"[{timestamp}] {message}", emoji, fallback)

def check_redis_connection():
    """Check if Redis is running and accessible."""
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        print_status("Redis connection successful", "SUCCESS")
        return True
    except Exception as e:
        print_status(f"Redis connection failed: {e}", "ERROR")
        print_status("Please ensure Redis is installed and running:", "INFO")
        print_status("  Windows: Download from https://github.com/microsoftarchive/redis/releases", "INFO")
        print_status("  Or use Docker: docker run -d -p 6379:6379 redis:alpine", "INFO")
        return False

def check_dependencies():
    """Check if all required dependencies are installed."""
    required_packages = ['celery', 'redis', 'asyncpg', 'python-dotenv']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print_status(f"Missing packages: {', '.join(missing_packages)}", "ERROR")
        print_status(f"Install with: pip install {' '.join(missing_packages)}", "INFO")
        return False
    
    print_status("All dependencies are installed", "SUCCESS")
    return True

def start_worker():
    """Start Celery worker."""
    print_status("Starting Celery worker...", "PROCESSING")
    
    cmd = [
        sys.executable, '-m', 'celery',
        '-A', 'celery_app',
        'worker',
        '--loglevel=info',
        '--concurrency=1',
        '--hostname=worker@%h'
    ]
    
    try:
        process = subprocess.Popen(cmd, cwd=os.getcwd())
        print_status(f"Celery worker started with PID: {process.pid}", "SUCCESS")
        
        # Save PID for later management
        with open('celery_worker.pid', 'w') as f:
            f.write(str(process.pid))
        
        return process
    except Exception as e:
        print_status(f"Failed to start worker: {e}", "ERROR")
        return None

def start_beat():
    """Start Celery beat scheduler."""
    print_status("Starting Celery beat scheduler...", "PROCESSING")
    
    cmd = [
        sys.executable, '-m', 'celery',
        '-A', 'celery_app',
        'beat',
        '--loglevel=info',
        '--schedule=celerybeat-schedule',
        '--pidfile=celerybeat.pid'
    ]
    
    try:
        process = subprocess.Popen(cmd, cwd=os.getcwd())
        print_status(f"Celery beat started with PID: {process.pid}", "SUCCESS")
        return process
    except Exception as e:
        print_status(f"Failed to start beat: {e}", "ERROR")
        return None

def start_all():
    """Start both worker and beat."""
    print_header("STARTING CELERY SERVICES")
    
    # Check prerequisites
    if not check_dependencies() or not check_redis_connection():
        return False
    
    # Start worker
    worker_process = start_worker()
    if not worker_process:
        return False
    
    time.sleep(2)  # Give worker time to start
    
    # Start beat
    beat_process = start_beat()
    if not beat_process:
        return False
    
    print_status("All Celery services started successfully!", "SUCCESS")
    print_status("Press Ctrl+C to stop all services", "INFO")
    
    try:
        # Wait for processes
        while True:
            time.sleep(1)
            if worker_process.poll() is not None:
                print_status("Worker process terminated", "WARNING")
                break
            if beat_process.poll() is not None:
                print_status("Beat process terminated", "WARNING")
                break
    except KeyboardInterrupt:
        print_status("Stopping services...", "PROCESSING")
        stop_all()
    
    return True

def stop_all():
    """Stop all Celery processes."""
    print_status("Stopping all Celery processes...", "PROCESSING")
    
    # Stop worker
    try:
        if os.path.exists('celery_worker.pid'):
            with open('celery_worker.pid', 'r') as f:
                pid = int(f.read().strip())
            os.kill(pid, signal.SIGTERM)
            os.remove('celery_worker.pid')
            print_status("Worker stopped", "SUCCESS")
    except Exception as e:
        print_status(f"Error stopping worker: {e}", "WARNING")
    
    # Stop beat
    try:
        if os.path.exists('celerybeat.pid'):
            with open('celerybeat.pid', 'r') as f:
                pid = int(f.read().strip())
            os.kill(pid, signal.SIGTERM)
            os.remove('celerybeat.pid')
            print_status("Beat stopped", "SUCCESS")
    except Exception as e:
        print_status(f"Error stopping beat: {e}", "WARNING")
    
    # Clean up schedule file
    if os.path.exists('celerybeat-schedule'):
        try:
            os.remove('celerybeat-schedule')
        except:
            pass

def show_status():
    """Show status of Celery processes."""
    print_header("CELERY STATUS")
    
    # Check worker status
    worker_running = False
    if os.path.exists('celery_worker.pid'):
        try:
            with open('celery_worker.pid', 'r') as f:
                pid = int(f.read().strip())
            os.kill(pid, 0)  # Check if process exists
            worker_running = True
            print_status(f"Worker is running (PID: {pid})", "SUCCESS")
        except:
            print_status("Worker is not running", "WARNING")
            os.remove('celery_worker.pid')
    else:
        print_status("Worker is not running", "WARNING")
    
    # Check beat status
    beat_running = False
    if os.path.exists('celerybeat.pid'):
        try:
            with open('celerybeat.pid', 'r') as f:
                pid = int(f.read().strip())
            os.kill(pid, 0)  # Check if process exists
            beat_running = True
            print_status(f"Beat is running (PID: {pid})", "SUCCESS")
        except:
            print_status("Beat is not running", "WARNING")
            os.remove('celerybeat.pid')
    else:
        print_status("Beat is not running", "WARNING")
    
    # Check Redis
    redis_running = check_redis_connection()
    
    # Overall status
    if worker_running and beat_running and redis_running:
        print_status("All services are running normally", "SUCCESS")
    else:
        print_status("Some services are not running", "WARNING")
    
    # Show recent execution reports
    show_recent_reports()

def show_recent_reports():
    """Show recent execution reports."""
    print("\n📊 Recent Execution Reports:")
    print("-" * 50)
    
    try:
        report_files = list(Path('.').glob('execution_report_*.json'))
        if report_files:
            # Get the 3 most recent reports
            recent_files = sorted(report_files, key=lambda x: x.stat().st_mtime, reverse=True)[:3]
            
            for report_file in recent_files:
                try:
                    with open(report_file, 'r') as f:
                        report = json.load(f)
                    
                    status = report.get('status', 'Unknown')
                    completed_at = report.get('completed_at_ist', report.get('failed_at_ist', 'Unknown'))
                    
                    if status == 'SUCCESS':
                        stats = report.get('scraper_stats', {})
                        processed = stats.get('processed', 0)
                        print_status(f"{completed_at}: SUCCESS - {processed} courses processed", "SUCCESS")
                    else:
                        error = report.get('error', 'Unknown error')
                        print_status(f"{completed_at}: {status} - {error}", "ERROR")
                        
                except Exception as e:
                    print_status(f"Error reading {report_file}: {e}", "WARNING")
        else:
            print_status("No execution reports found", "INFO")
            
    except Exception as e:
        print_status(f"Error checking reports: {e}", "WARNING")

def test_task():
    """Run a test scraper task."""
    print_header("TESTING SCRAPER TASK")
    
    try:
        from celery_app import app
        from celery_tasks import run_udemy_scraper
        
        print_status("Sending test task to queue...", "PROCESSING")
        
        # Send task
        result = run_udemy_scraper.delay()
        print_status(f"Task sent with ID: {result.id}", "SUCCESS")
        
        print_status("Waiting for task completion...", "PROCESSING")
        
        # Wait for result with timeout
        try:
            task_result = result.get(timeout=3600)  # 1 hour timeout
            print_status("Task completed successfully!", "SUCCESS")
            print(json.dumps(task_result, indent=2))
        except Exception as e:
            print_status(f"Task failed or timed out: {e}", "ERROR")
            
    except Exception as e:
        print_status(f"Error running test task: {e}", "ERROR")

def start_monitor():
    """Start Celery monitoring."""
    print_header("CELERY MONITORING")
    
    cmd = [
        sys.executable, '-m', 'celery',
        '-A', 'celery_app',
        'events',
        '--camera=celery.events.state.State'
    ]
    
    try:
        print_status("Starting Celery monitoring...", "PROCESSING")
        subprocess.run(cmd, cwd=os.getcwd())
    except KeyboardInterrupt:
        print_status("Monitoring stopped", "INFO")
    except Exception as e:
        print_status(f"Error starting monitor: {e}", "ERROR")

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Manage Celery services for Udemy scraper')
    parser.add_argument('command', choices=[
        'start-worker', 'start-beat', 'start-all', 'stop-all', 
        'status', 'test-task', 'monitor'
    ], help='Command to execute')
    
    args = parser.parse_args()
    
    if args.command == 'start-worker':
        start_worker()
    elif args.command == 'start-beat':
        start_beat()
    elif args.command == 'start-all':
        start_all()
    elif args.command == 'stop-all':
        stop_all()
    elif args.command == 'status':
        show_status()
    elif args.command == 'test-task':
        test_task()
    elif args.command == 'monitor':
        start_monitor()

if __name__ == '__main__':
    main()
