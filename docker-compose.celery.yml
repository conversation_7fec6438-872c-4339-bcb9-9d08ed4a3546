# Docker Compose for Celery-based Udemy Scraper Automation
# =========================================================

version: '3.8'

services:
  # Redis broker for Celery
  redis:
    image: redis:7-alpine
    container_name: udemy-scraper-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery worker
  celery-worker:
    build:
      context: .
      dockerfile: Dockerfile.celery
    container_name: udemy-scraper-worker
    depends_on:
      - redis
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - ENVIRONMENT=production
    volumes:
      - ./backend:/app/backend
      - ./logs:/app/logs
      - celery_data:/app/data
    command: celery -A celery_app worker --loglevel=info --concurrency=1
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "celery", "-A", "celery_app", "inspect", "ping"]
      interval: 60s
      timeout: 30s
      retries: 3

  # Celery beat scheduler
  celery-beat:
    build:
      context: .
      dockerfile: Dockerfile.celery
    container_name: udemy-scraper-beat
    depends_on:
      - redis
      - celery-worker
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - ENVIRONMENT=production
    volumes:
      - ./backend:/app/backend
      - ./logs:/app/logs
      - celery_data:/app/data
    command: celery -A celery_app beat --loglevel=info --schedule=/app/data/celerybeat-schedule
    restart: unless-stopped

  # Flower monitoring (optional)
  flower:
    build:
      context: .
      dockerfile: Dockerfile.celery
    container_name: udemy-scraper-flower
    depends_on:
      - redis
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    ports:
      - "5555:5555"
    volumes:
      - celery_data:/app/data
    command: celery -A celery_app flower --port=5555
    restart: unless-stopped
    profiles:
      - monitoring

volumes:
  redis_data:
    driver: local
  celery_data:
    driver: local

networks:
  default:
    name: udemy-scraper-network
