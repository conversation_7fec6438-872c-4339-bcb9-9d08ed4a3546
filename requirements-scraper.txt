# Enhanced Udemy Course Scraper Dependencies
# Production-ready dependencies for the automated scraper

# Core dependencies
asyncio-timeout==5.0.1
asyncpg==0.30.0
python-dotenv==1.0.1

# Web scraping
playwright==1.48.0

# Progress tracking and utilities
tqdm==4.66.1

# HTTP client for health checks
httpx==0.28.1

# Logging and monitoring
structlog==23.2.0

# Data processing
python-dateutil==2.8.2

# Database connection pooling (already included in asyncpg but explicit)
# asyncpg handles connection pooling internally

# Optional: For advanced monitoring and alerting
psutil==5.9.6

# Optional: For configuration management
pydantic==2.10.6
pydantic-settings==2.5.2
