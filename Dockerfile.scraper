# Enhanced Udemy Course Scraper Dockerfile
# This container runs the automated scraper with scheduled execution

FROM python:3.11-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive

# Install system dependencies including Chrome
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    cron \
    curl \
    unzip \
    && wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add - \
    && sh -c 'echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google.list' \
    && apt-get update \
    && apt-get install -y google-chrome-stable \
    && rm -rf /var/lib/apt/lists/*

# Create application directory
WORKDIR /app

# Create logs directory
RUN mkdir -p /app/logs

# Copy requirements and install Python dependencies
COPY requirements-scraper.txt .
RUN pip install --no-cache-dir -r requirements-scraper.txt

# Install Playwright and its dependencies
R<PERSON> playwright install chromium
RUN playwright install-deps chromium

# Copy application code
COPY backend/udemy_course_scraper_enhanced.py .
COPY backend/.env .env
COPY run_scraper.sh .
COPY scraper_config.py .

# Make shell script executable
RUN chmod +x run_scraper.sh

# Set up cron job for scheduled execution
# Run every 6 hours: 0 */6 * * *
# Run daily at 2 AM: 0 2 * * *
# Run twice daily at 2 AM and 2 PM: 0 2,14 * * *
RUN echo "0 */6 * * * /app/run_scraper.sh >> /app/logs/scraper_cron.log 2>&1" > /etc/cron.d/scraper-cron
RUN chmod 0644 /etc/cron.d/scraper-cron
RUN crontab /etc/cron.d/scraper-cron

# Create startup script
RUN echo '#!/bin/bash\n\
echo "Starting Enhanced Udemy Course Scraper Container..."\n\
echo "Cron schedule: Every 6 hours"\n\
echo "Logs location: /app/logs/"\n\
echo "Configuration loaded from: /app/.env"\n\
\n\
# Start cron daemon\n\
cron\n\
\n\
# Keep container running and show logs\n\
echo "Container started. Monitoring logs..."\n\
tail -f /app/logs/scraper_cron.log /app/udemy_scraper_enhanced.log 2>/dev/null || sleep infinity\n\
' > /app/start.sh && chmod +x /app/start.sh

# Health check
HEALTHCHECK --interval=30m --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Expose port for health checks (optional)
EXPOSE 8080

# Run the startup script
CMD ["/app/start.sh"]
