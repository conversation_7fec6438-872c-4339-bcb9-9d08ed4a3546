#!/usr/bin/env python3
"""
Quick test script to verify the enhanced scraper setup
"""

import os
import sys
import asyncio
import asyncpg
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def test_database_connection():
    """Test database connectivity."""
    print("🧪 Testing database connection...")
    
    try:
        DB_CONFIG = {
            "user": os.getenv("DB_USER"),
            "password": os.getenv("DB_PASSWORD"),
            "database": os.getenv("DB_NAME"),
            "host": os.getenv("DB_HOST"),
            "port": os.getenv("DB_PORT", "5432"),
        }
        
        print(f"Connecting to: {DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']} as {DB_CONFIG['user']}")
        
        conn = await asyncpg.connect(**DB_CONFIG)
        result = await conn.fetchval("SELECT 1")
        
        if result == 1:
            print("✅ Database connection successful!")
            
            # Check if table exists
            table_exists = await conn.fetchval("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'udemy_course_catalog'
                )
            """)
            
            if table_exists:
                print("✅ udemy_course_catalog table exists")
                
                # Count courses
                total_courses = await conn.fetchval("SELECT COUNT(*) FROM udemy_course_catalog")
                print(f"📊 Total courses in database: {total_courses}")
                
                # Count IT courses
                it_courses = await conn.fetchval(
                    "SELECT COUNT(*) FROM udemy_course_catalog WHERE is_it_course = TRUE"
                )
                print(f"📊 IT courses: {it_courses}")
                
            else:
                print("⚠️ udemy_course_catalog table not found")
                
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def test_imports():
    """Test if all required packages can be imported."""
    print("🧪 Testing imports...")
    
    try:
        import asyncpg
        print("✅ asyncpg imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import asyncpg: {e}")
        return False
    
    try:
        from playwright.async_api import async_playwright
        print("✅ playwright imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import playwright: {e}")
        return False
    
    try:
        from dotenv import load_dotenv
        print("✅ python-dotenv imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import python-dotenv: {e}")
        return False
    
    return True

def test_environment_variables():
    """Test if required environment variables are set."""
    print("🧪 Testing environment variables...")
    
    required_vars = ['DB_HOST', 'DB_USER', 'DB_PASSWORD', 'DB_NAME']
    all_good = True
    
    for var in required_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var} is set")
        else:
            print(f"❌ {var} is not set")
            all_good = False
    
    return all_good

async def main():
    """Run all tests."""
    print("🚀 Enhanced Udemy Course Scraper - Quick Test")
    print("=" * 50)
    
    # Test imports
    if not test_imports():
        print("❌ Import test failed")
        return False
    
    # Test environment variables
    if not test_environment_variables():
        print("❌ Environment variables test failed")
        return False
    
    # Test database connection
    if not await test_database_connection():
        print("❌ Database connection test failed")
        return False
    
    print("\n🎉 All tests passed! The enhanced scraper should work correctly.")
    return True

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except Exception as e:
        print(f"💥 Test failed with error: {e}")
        sys.exit(1)
