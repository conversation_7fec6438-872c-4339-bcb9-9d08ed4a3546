# Celery Automation System Requirements
# ====================================

# Core Celery dependencies
celery[redis]==5.3.4
redis==5.0.1

# Database connectivity (already in main requirements)
asyncpg==0.29.0

# Environment and configuration
python-dotenv==1.0.0

# Additional utilities
psutil==5.9.6  # For system monitoring
pathlib2==2.3.7  # Enhanced path handling (Python < 3.4 compatibility)

# Optional monitoring and debugging
flower==2.0.1  # Web-based monitoring for Celery
celery-beat-scheduler==1.0.0  # Enhanced beat scheduler

# Logging and monitoring
structlog==23.2.0  # Structured logging
colorlog==6.8.0  # Colored console logging

# Development and testing
pytest-celery==0.0.0a1  # Testing utilities for Celery
