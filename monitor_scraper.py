#!/usr/bin/env python3
"""
Enhanced Udemy Scraper Monitoring Script
========================================

This script provides real-time monitoring of the Udemy course scraper with:
- Live log streaming
- Performance metrics
- Status dashboard
- Error tracking

Usage:
    python monitor_scraper.py [--force-run] [--test-mode]
"""

import os
import sys
import time
import json
import subprocess
import argparse
from datetime import datetime
from pathlib import Path

def print_header():
    """Print monitoring dashboard header."""
    print("=" * 80)
    print("🚀 ENHANCED UDEMY COURSE SCRAPER MONITOR")
    print("=" * 80)
    print(f"📅 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)

def print_status(message, status_type="INFO"):
    """Print formatted status message."""
    timestamp = datetime.now().strftime('%H:%M:%S')
    icons = {
        "INFO": "ℹ️",
        "SUCCESS": "✅", 
        "WARNING": "⚠️",
        "ERROR": "❌",
        "PROCESSING": "🔄"
    }
    icon = icons.get(status_type, "📝")
    print(f"[{timestamp}] {icon} {message}")

def check_database_connection():
    """Check if database is accessible."""
    print_status("Checking database connection...", "PROCESSING")
    try:
        import asyncpg
        import asyncio
        from dotenv import load_dotenv
        
        load_dotenv('backend/.env')
        
        async def test_connection():
            conn = await asyncpg.connect(
                host=os.getenv('DB_HOST'),
                port=os.getenv('DB_PORT'),
                user=os.getenv('DB_USER'),
                password=os.getenv('DB_PASSWORD'),
                database=os.getenv('DB_NAME')
            )
            
            # Test query
            result = await conn.fetchval('SELECT COUNT(*) FROM udemy_course_catalog')
            await conn.close()
            return result
        
        course_count = asyncio.run(test_connection())
        print_status(f"Database connected! Found {course_count:,} courses in catalog", "SUCCESS")
        return True
        
    except Exception as e:
        print_status(f"Database connection failed: {e}", "ERROR")
        return False

def get_courses_needing_update():
    """Get count of courses that need updating."""
    try:
        import asyncpg
        import asyncio
        from dotenv import load_dotenv
        
        load_dotenv('backend/.env')
        
        async def get_count():
            conn = await asyncpg.connect(
                host=os.getenv('DB_HOST'),
                port=os.getenv('DB_PORT'),
                user=os.getenv('DB_USER'),
                password=os.getenv('DB_PASSWORD'),
                database=os.getenv('DB_NAME')
            )
            
            result = await conn.fetchval("""
                SELECT COUNT(*) FROM udemy_course_catalog
                WHERE course_language = 'en'
                    AND is_it_course = TRUE
                    AND product_url IS NOT NULL
                    AND (
                        last_updated IS NULL
                        OR last_updated < (CURRENT_TIMESTAMP - INTERVAL '7 days')
                    )
            """)
            await conn.close()
            return result
        
        count = asyncio.run(get_count())
        return count
        
    except Exception as e:
        print_status(f"Error checking courses: {e}", "ERROR")
        return 0

def run_scraper_with_monitoring(force_run=False, test_mode=False):
    """Run the scraper with real-time monitoring."""
    
    # Check prerequisites
    if not check_database_connection():
        print_status("Cannot proceed without database connection", "ERROR")
        return False
    
    # Check if scraping is needed
    courses_to_update = get_courses_needing_update()
    
    if courses_to_update == 0 and not force_run:
        print_status("No courses need updating (all updated within 7 days)", "INFO")
        print_status("Use --force-run to run anyway", "INFO")
        return True
    
    print_status(f"Found {courses_to_update:,} courses that need updating", "INFO")
    
    if test_mode:
        print_status("TEST MODE: Would run scraper now", "WARNING")
        return True
    
    # Run the scraper
    print_status("Starting Enhanced Udemy Course Scraper...", "PROCESSING")
    print("-" * 80)
    
    try:
        # Run scraper with real-time output
        process = subprocess.Popen(
            [sys.executable, "backend/udemy_course_scraper_enhanced.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            encoding='utf-8',
            errors='replace',
            bufsize=1
        )
        
        # Stream output in real-time
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                print(output.strip())
        
        # Get return code
        return_code = process.poll()
        
        print("-" * 80)
        if return_code == 0:
            print_status("Scraper completed successfully!", "SUCCESS")
            
            # Show stats if available
            stats_file = Path("backend/scraper_stats.json")
            if stats_file.exists():
                try:
                    with open(stats_file, 'r') as f:
                        stats = json.load(f)
                    
                    print_status("Final Statistics:", "INFO")
                    print(f"   📊 Total courses processed: {stats.get('total_courses', 0)}")
                    print(f"   ✅ Successfully processed: {stats.get('processed', 0)}")
                    print(f"   ⏩ Skipped (recent): {stats.get('skipped', 0)}")
                    print(f"   ❌ Errors: {stats.get('errors', 0)}")
                    print(f"   💯 Success rate: {stats.get('success_rate', 0)}%")
                    print(f"   🚀 Processing rate: {stats.get('courses_per_hour', 0)} courses/hour")
                    print(f"   ⏱️  Total time: {stats.get('elapsed_time', 0)} seconds")
                    
                except Exception as e:
                    print_status(f"Could not read stats: {e}", "WARNING")
            
            return True
        else:
            print_status(f"Scraper failed with return code: {return_code}", "ERROR")
            return False
            
    except KeyboardInterrupt:
        print_status("Scraper interrupted by user", "WARNING")
        if 'process' in locals():
            process.terminate()
        return False
    except Exception as e:
        print_status(f"Error running scraper: {e}", "ERROR")
        return False

def main():
    """Main monitoring function."""
    parser = argparse.ArgumentParser(description="Monitor Enhanced Udemy Course Scraper")
    parser.add_argument("--force-run", action="store_true", 
                       help="Force run even if no courses need updating")
    parser.add_argument("--test-mode", action="store_true",
                       help="Test mode - check status but don't run scraper")
    
    args = parser.parse_args()
    
    print_header()
    
    success = run_scraper_with_monitoring(
        force_run=args.force_run,
        test_mode=args.test_mode
    )
    
    print("=" * 80)
    if success:
        print_status("Monitoring session completed successfully", "SUCCESS")
    else:
        print_status("Monitoring session completed with issues", "WARNING")
    print("=" * 80)

if __name__ == "__main__":
    main()
