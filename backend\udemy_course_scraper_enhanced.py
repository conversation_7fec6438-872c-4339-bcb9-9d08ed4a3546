#!/usr/bin/env python3
"""
Enhanced Asynchronous Udemy Course Scraper
==========================================

This is the production-ready enhanced version of the Udemy course scraper with:
- Incremental updates (only scrape courses not updated in last 7 days)
- Robust error handling with exponential backoff
- Connection pooling for efficient database operations
- Comprehensive logging and monitoring
- Performance metrics tracking
- Graceful handling of rate limits and timeouts

Author: Mentoring Agent Development Team
Version: 2.0.0
"""

import os
import re
import asyncio
import logging
import asyncpg
import datetime
import time
import json
from typing import Tuple, Dict, Any, Optional
from dotenv import load_dotenv
from playwright.async_api import async_playwright, TimeoutError as PlaywrightTimeoutError

# Try to import tqdm.asyncio, fallback to regular tqdm if not available
try:
    from tqdm.asyncio import tqdm_asyncio
except ImportError:
    # Fallback for systems without tqdm.asyncio
    import asyncio as tqdm_asyncio
    tqdm_asyncio.gather = asyncio.gather

# Load environment variables
load_dotenv()

# Configuration
DB_CONFIG = {
    "user": os.getenv("DB_USER"),
    "password": os.getenv("DB_PASSWORD"),
    "database": os.getenv("DB_NAME"),
    "host": os.getenv("DB_HOST"),
    "port": os.getenv("DB_PORT", "5432"),
}

# Scraper Configuration
MAX_CONCURRENT_BROWSERS = int(os.getenv("SCRAPER_MAX_CONCURRENT", "5"))
UPDATE_INTERVAL_DAYS = int(os.getenv("SCRAPER_UPDATE_INTERVAL_DAYS", "7"))
REQUEST_TIMEOUT = int(os.getenv("SCRAPER_REQUEST_TIMEOUT", "15000"))
MAX_RETRIES = int(os.getenv("SCRAPER_MAX_RETRIES", "3"))
RETRY_DELAY_BASE = float(os.getenv("SCRAPER_RETRY_DELAY_BASE", "1.5"))

# Setup comprehensive logging
LOG_FORMAT = "%(asctime)s - %(levelname)s - [%(funcName)s:%(lineno)d] - %(message)s"
logging.basicConfig(
    level=logging.INFO,
    format=LOG_FORMAT,
    handlers=[
        logging.FileHandler("udemy_scraper_enhanced.log", mode="a", encoding="utf-8"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Performance tracking
class PerformanceTracker:
    def __init__(self):
        self.start_time = time.time()
        self.processed = 0
        self.skipped = 0
        self.errors = 0
        self.failed = 0

    def log_result(self, result: str):
        if result == "processed":
            self.processed += 1
        elif result == "skipped":
            self.skipped += 1
        elif result == "error":
            self.errors += 1
        elif result == "failed":
            self.failed += 1

    def get_stats(self) -> Dict[str, Any]:
        elapsed = time.time() - self.start_time
        total = self.processed + self.skipped + self.errors + self.failed

        # Get current time in IST
        ist_timezone = datetime.timezone(datetime.timedelta(hours=5, minutes=30))
        current_time_ist = datetime.datetime.now(ist_timezone)

        return {
            "elapsed_time": round(elapsed, 2),
            "total_courses": total,
            "processed": self.processed,
            "skipped": self.skipped,
            "errors": self.errors,
            "failed": self.failed,
            "success_rate": round((self.processed / total * 100) if total > 0 else 0, 2),
            "courses_per_hour": round((total / elapsed * 3600) if elapsed > 0 else 0, 2),
            "last_scraping_completed": current_time_ist.strftime("%Y-%m-%d %H:%M:%S IST"),
            "last_scraping_completed_iso": current_time_ist.isoformat()
        }

def get_ist_time() -> str:
    """Get current time in IST timezone formatted for display."""
    ist_timezone = datetime.timezone(datetime.timedelta(hours=5, minutes=30))
    current_time_ist = datetime.datetime.now(ist_timezone)
    return current_time_ist.strftime("%Y-%m-%d %H:%M:%S IST")

def parse_duration(iso_duration: str) -> str:
    """Parse ISO 8601 duration format to PostgreSQL interval format."""
    if not iso_duration or iso_duration in ["PT0M", "P0D"]:
        return None  # Return None for NULL in database

    try:
        # Handle PT format (e.g., PT9H24M, PT42M, PT0M)
        if iso_duration.startswith('PT'):
            matches = re.match(r'PT(?:(\d+)H)?(?:(\d+)M)?', iso_duration)
            if not matches:
                return None

            hours = int(matches.group(1)) if matches.group(1) else 0
            minutes = int(matches.group(2)) if matches.group(2) else 0

            # Return PostgreSQL interval format
            if hours > 0 and minutes > 0:
                return f"{hours:02d}:{minutes:02d}:00"
            elif hours > 0:
                return f"{hours:02d}:00:00"
            elif minutes > 0:
                return f"00:{minutes:02d}:00"
            else:
                return None

        # Handle P format with days (e.g., P1DT2H44M)
        elif iso_duration.startswith('P') and 'T' in iso_duration:
            # Extract days and time parts
            parts = iso_duration.split('T')
            day_part = parts[0][1:]  # Remove 'P'
            time_part = parts[1]

            days = 0
            hours = 0
            minutes = 0

            # Extract days
            days_match = re.search(r'(\d+)D', day_part)
            if days_match:
                days = int(days_match.group(1))

            # Extract hours and minutes from time part
            hours_match = re.search(r'(\d+)H', time_part)
            if hours_match:
                hours = int(hours_match.group(1))

            minutes_match = re.search(r'(\d+)M', time_part)
            if minutes_match:
                minutes = int(minutes_match.group(1))

            # Convert to total hours and minutes
            total_hours = days * 24 + hours
            return f"{total_hours:02d}:{minutes:02d}:00"

        # Handle simple P0D format
        elif iso_duration == "P0D":
            return None

        # Fallback for other formats
        return None

    except Exception as e:
        logger.warning(f"Error parsing duration '{iso_duration}': {e}")
        return None

def clean_numeric_value(value: str) -> Optional[float]:
    """Clean and validate numeric values from scraped data."""
    if not value or value == "N/A":
        return None

    # Remove common formatting characters
    cleaned = re.sub(r'[,\s]', '', str(value))

    # Extract numeric part
    numeric_match = re.search(r'[\d.]+', cleaned)
    if numeric_match:
        try:
            return float(numeric_match.group())
        except ValueError:
            return None

    return None

def clean_integer_value(value: str) -> Optional[int]:
    """Clean and validate integer values from scraped data."""
    if not value or value == "N/A":
        return None

    # Remove common formatting characters
    cleaned = re.sub(r'[,\s]', '', str(value))

    # Extract numeric part
    numeric_match = re.search(r'\d+', cleaned)
    if numeric_match:
        try:
            return int(numeric_match.group())
        except ValueError:
            return None

    return None

async def ensure_database_schema(db_pool: asyncpg.Pool):
    """Ensure the database has the required schema for enhanced scraping."""
    logger.info("🔧 Ensuring database schema is up to date...")
    
    async with db_pool.acquire() as conn:
        # Add last_updated column if it doesn't exist
        await conn.execute("""
            DO $$
            BEGIN
                IF NOT EXISTS (
                    SELECT 1 FROM information_schema.columns 
                    WHERE table_name = 'udemy_course_catalog' AND column_name = 'last_updated'
                ) THEN
                    ALTER TABLE udemy_course_catalog ADD COLUMN last_updated TIMESTAMP;
                    CREATE INDEX IF NOT EXISTS idx_last_updated ON udemy_course_catalog(last_updated);
                    CREATE INDEX IF NOT EXISTS idx_course_language ON udemy_course_catalog(course_language);
                    CREATE INDEX IF NOT EXISTS idx_is_it_course ON udemy_course_catalog(is_it_course);
                END IF;
            END
            $$;
        """)
        


        # Ensure other required columns exist
        required_columns = {
            "product_subtitle": "TEXT",
            "rating_value": "TEXT",
            "rating_count": "TEXT",
            "enrollments": "TEXT",
            "video_content_length": "TEXT"
        }

        for col, col_type in required_columns.items():
            await conn.execute(f"""
                DO $$
                BEGIN
                    IF NOT EXISTS (
                        SELECT 1 FROM information_schema.columns
                        WHERE table_name = 'udemy_course_catalog' AND column_name = '{col}'
                    ) THEN
                        ALTER TABLE udemy_course_catalog ADD COLUMN {col} {col_type};
                    END IF;
                END
                $$;
            """)
    
    logger.info("✅ Database schema verification completed")

async def scrape_course(sem: asyncio.Semaphore, browser, course: Tuple, db_pool: asyncpg.Pool) -> str:
    """
    Scrapes a single course with retry logic and incremental update checking.

    Args:
        sem: Semaphore for controlling concurrency
        browser: Shared Playwright browser instance
        course: Tuple containing (product_id, url, last_updated, product_name)
        db_pool: Database connection pool

    Returns:
        Status string: "processed", "skipped", "error", or "failed"
    """
    product_id, url, last_updated, current_title = course
    
    # Skip if updated recently (incremental update logic)
    if last_updated:
        try:
            if isinstance(last_updated, str):
                last_updated_date = datetime.datetime.fromisoformat(last_updated.replace('Z', '+00:00'))
            else:
                last_updated_date = last_updated
            
            days_since_update = (datetime.datetime.now(datetime.timezone.utc) - last_updated_date.replace(tzinfo=datetime.timezone.utc)).days
            
            if days_since_update < UPDATE_INTERVAL_DAYS:
                logger.info(f"⏩ Skipping Course: {current_title or product_id} (Updated {days_since_update} days ago)")
                return "skipped"
        except Exception as e:
            logger.warning(f"Error parsing last_updated for {product_id}: {e}. Will proceed with scraping.")
    
    async with sem:  # Control concurrency
        for attempt in range(1, MAX_RETRIES + 1):
            try:
                context = await browser.new_context(
                    user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                )
                page = await context.new_page()
                
                # Navigate to the course page with timeout
                await page.goto(url, wait_until="domcontentloaded", timeout=REQUEST_TIMEOUT)
                
                # Extract course data using optimized JavaScript evaluation
                course_data = await page.evaluate('''() => {
                    try {
                        const jsonLD = document.querySelector('script[type="application/ld+json"]');
                        const schema = jsonLD ? JSON.parse(jsonLD.textContent)['@graph'][0] : {};
                        
                        return {
                            title: schema.name || document.querySelector('h1.udlite-heading-xl')?.innerText?.trim() || 'N/A',
                            subtitle: schema.description || document.querySelector("div[data-purpose='lead-content']")?.innerText?.trim() || 'N/A',
                            ratingValue: schema.aggregateRating?.ratingValue || document.querySelector("span[data-purpose='rating-number']")?.innerText?.trim() || 'N/A',
                            ratingCount: schema.aggregateRating?.ratingCount || document.querySelector("span[data-purpose='ratings-count']")?.innerText?.trim() || 'N/A',
                            enrollments: document.querySelector("div[data-purpose='enrollment']")?.innerText?.trim() || 'N/A',
                            videoLength: schema.hasCourseInstance?.courseWorkload || 'PT0M'
                        };
                    } catch (error) {
                        console.error('Error extracting course data:', error);
                        return {
                            title: 'N/A',
                            subtitle: 'N/A', 
                            ratingValue: 'N/A',
                            ratingCount: 'N/A',
                            enrollments: 'N/A',
                            videoLength: 'PT0M'
                        };
                    }
                }''')
                
                await context.close()
                
                # Process and clean the extracted data
                # Parse video duration from ISO format to PostgreSQL interval format
                video_length = parse_duration(course_data['videoLength']) if course_data['videoLength'] else None
                rating_value = clean_numeric_value(course_data['ratingValue'])
                rating_count = clean_integer_value(course_data['ratingCount'])
                enrollments = clean_integer_value(course_data['enrollments'])

                # Update database with extracted data
                async with db_pool.acquire() as conn:
                    # Handle INTERVAL type for video_content_length
                    if video_length:
                        # Use PostgreSQL's MAKE_INTERVAL function for proper INTERVAL creation
                        parts = video_length.split(':')
                        hours = int(parts[0])
                        minutes = int(parts[1])

                        await conn.execute("""
                            UPDATE udemy_course_catalog SET
                                product_name = $1,
                                product_subtitle = $2,
                                rating_value = $3,
                                rating_count = $4,
                                enrollments = $5,
                                video_content_length = MAKE_INTERVAL(hours => $6, mins => $7),
                                last_updated = CURRENT_TIMESTAMP
                            WHERE product_id = $8;
                        """,
                        course_data['title'],
                        course_data['subtitle'],
                        rating_value,  # Send as float
                        rating_count,  # Send as int
                        enrollments,   # Send as int
                        hours,         # Send hours as int
                        minutes,       # Send minutes as int
                        product_id)
                    else:
                        # Handle NULL video_content_length
                        await conn.execute("""
                            UPDATE udemy_course_catalog SET
                                product_name = $1,
                                product_subtitle = $2,
                                rating_value = $3,
                                rating_count = $4,
                                enrollments = $5,
                                video_content_length = NULL,
                                last_updated = CURRENT_TIMESTAMP
                            WHERE product_id = $6;
                        """,
                        course_data['title'],
                        course_data['subtitle'],
                        rating_value,  # Send as float
                        rating_count,  # Send as int
                        enrollments,   # Send as int
                        product_id)
                
                logger.info(f"✅ Processed Course: {course_data['title']} (Product ID: {product_id})")
                return "processed"
                
            except PlaywrightTimeoutError as e:
                logger.warning(f"⏰ Timeout on attempt {attempt}/{MAX_RETRIES} for Product ID: {product_id} - {str(e)}")
                if attempt < MAX_RETRIES:
                    # Exponential backoff
                    delay = RETRY_DELAY_BASE ** attempt
                    await asyncio.sleep(delay)
                    continue
                else:
                    logger.error(f"❌ Failed after {MAX_RETRIES} timeout attempts: Product ID {product_id}")
                    return "failed"
                    
            except Exception as e:
                logger.error(f"❌ Error on attempt {attempt}/{MAX_RETRIES} for Product ID: {product_id} - {str(e)}")
                if attempt < MAX_RETRIES:
                    delay = RETRY_DELAY_BASE ** attempt
                    await asyncio.sleep(delay)
                    continue
                else:
                    logger.error(f"❌ Failed after {MAX_RETRIES} attempts: Product ID {product_id}")
                    return "error"
        
        return "failed"

async def get_courses_to_scrape(db_pool: asyncpg.Pool) -> list:
    """Fetch courses that need to be scraped based on incremental update logic."""
    logger.info("📋 Fetching courses that need scraping...")

    async with db_pool.acquire() as conn:
        # Get courses that haven't been updated recently or never updated
        rows = await conn.fetch("""
            SELECT product_id, product_url, last_updated, product_name
            FROM udemy_course_catalog
            WHERE course_language = 'en'
                AND is_it_course = TRUE
                AND product_url IS NOT NULL
                AND (
                    last_updated IS NULL
                    OR last_updated < (CURRENT_TIMESTAMP - INTERVAL '%s days')
                )
            ORDER BY
                CASE WHEN last_updated IS NULL THEN 0 ELSE 1 END,
                last_updated ASC
        """ % UPDATE_INTERVAL_DAYS)

        courses = [tuple(row) for row in rows]

    logger.info(f"📊 Found {len(courses)} courses that need scraping")
    return courses

async def main():
    """Main function to orchestrate the enhanced scraping process."""
    logger.info("🚀 Starting Enhanced Udemy Course Scraper v2.0.0")
    logger.info(f"⚙️  Configuration: Max Concurrent={MAX_CONCURRENT_BROWSERS}, Update Interval={UPDATE_INTERVAL_DAYS} days")
    logger.info(f"🕐 Started at: {get_ist_time()}")

    # Initialize performance tracker
    tracker = PerformanceTracker()

    try:
        # Create database connection pool
        logger.info("🔌 Creating database connection pool...")
        db_pool = await asyncpg.create_pool(
            **DB_CONFIG,
            min_size=2,
            max_size=10,
            command_timeout=60
        )

        # Ensure database schema is up to date
        await ensure_database_schema(db_pool)

        # Get courses that need scraping
        courses = await get_courses_to_scrape(db_pool)

        if not courses:
            logger.info("✅ No courses need scraping at this time")
            return

        logger.info(f"📚 Starting to scrape {len(courses)} courses...")

        # Create semaphore for concurrency control
        sem = asyncio.Semaphore(MAX_CONCURRENT_BROWSERS)

        # Launch browser and start scraping
        async with async_playwright() as p:
            browser = await p.chromium.launch(
                headless=True,
                args=[
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu'
                ]
            )

            # Create tasks for all courses
            tasks = []
            for course in courses:
                task = scrape_course(sem, browser, course, db_pool)
                tasks.append(task)

            # Execute all tasks with progress tracking
            logger.info("🔄 Processing courses...")
            results = await tqdm_asyncio.gather(*tasks, desc="Scraping courses")

            # Track results
            for result in results:
                tracker.log_result(result)

            await browser.close()

        # Close database pool
        await db_pool.close()

        # Log final statistics
        stats = tracker.get_stats()
        logger.info("📊 Scraping completed! Final Statistics:")
        logger.info(f"   ⏱️  Total time: {stats['elapsed_time']} seconds")
        logger.info(f"   📈 Total courses: {stats['total_courses']}")
        logger.info(f"   ✅ Processed: {stats['processed']}")
        logger.info(f"   ⏩ Skipped: {stats['skipped']}")
        logger.info(f"   ⚠️  Errors: {stats['errors']}")
        logger.info(f"   ❌ Failed: {stats['failed']}")
        logger.info(f"   📊 Success rate: {stats['success_rate']}%")
        logger.info(f"   🚀 Processing rate: {stats['courses_per_hour']} courses/hour")
        logger.info(f"   🕐 Last scraping completed: {stats['last_scraping_completed']}")

        # Write stats to file for monitoring
        try:
            stats_file_path = os.path.join(os.path.dirname(__file__), "scraper_stats.json")
            with open(stats_file_path, "w") as f:
                json.dump(stats, f, indent=2)
            logger.info(f"📊 Stats saved to: {stats_file_path}")
        except Exception as e:
            logger.warning(f"⚠️ Could not save stats file: {e}")

        logger.info("✅ Enhanced scraping process completed successfully!")

    except Exception as e:
        logger.error(f"💥 Critical error in main process: {str(e)}")
        raise

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("🛑 Scraping interrupted by user")
    except Exception as e:
        logger.error(f"💥 Fatal error: {str(e)}")
        exit(1)
