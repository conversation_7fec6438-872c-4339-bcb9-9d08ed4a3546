# Node.js
node_modules/
frontend/node_modules/
# Python
__pycache__/
*.py[cod]
*.pyo

# Environment variables
.env

# Logs
*.log

# Virtual environments
*venv

# Build artifacts
dist/
build/

# IDE specific files
.vscode/
.idea/

# MacOS specific files
.DS_Store

# Windows specific files
Thumbs.db

# Coverage reports
coverage/
*.cover
*.coverage

# Temporary files
*.tmp
*.temp

# Compiled files
*.out
*.o
*.class

# Miscellaneous
*.swp
*.swo
*.bak
*.old
*.orig
*.rej
*.sublime-workspace
*.sublime-project

# Dependencies
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

backend/mtab-venv
backend/.env
backend/udemy_course_pipeline.log
backend/udemy_courses_09032025.xml
backend/udemy_course_ingestion.log
backend/.log
backend/__pycache__
backend/__pycache__/main.cpython-39.pyc
backend/__pycache__/scraping.cpython-39.pyc
mtat-venv

udemy_cookies.json
udemy_local_storage.json

page_load.py
page_source.txt
page_source_rakuten.txt

# Test files
test_enhanced_scraper.py
test_scraper.py
test_scraper_config.py
test_scraper_monitor.py
test_scraper_pipeline.py
test_scraper_pipeline_config.py
test_scraper_pipeline_config.py
quick_test.py
force_update_test.py