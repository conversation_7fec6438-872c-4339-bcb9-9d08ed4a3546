# Celery-based Udemy Scraper Automation System

## Overview

This system provides automated, scheduled execution of the Udemy course scraper using <PERSON><PERSON><PERSON> with <PERSON><PERSON> as the message broker. The system runs the scraper every 7 days (weekly) with comprehensive monitoring, error handling, and alerting capabilities.

## 🚀 Key Features

### ⏰ **Automated Scheduling**
- **Weekly Execution**: Runs every Monday at 2:00 AM IST
- **Celery Beat**: Reliable cron-like scheduling
- **Timezone Aware**: All operations in IST (Indian Standard Time)
- **Configurable Intervals**: Easy to modify scheduling

### 🛡️ **Robust Error Handling**
- **Automatic Retries**: 3 retry attempts with exponential backoff
- **Graceful Degradation**: Continues operation despite individual failures
- **Database Validation**: Pre-execution connection checks
- **Timeout Protection**: 2-hour maximum execution time

### 📊 **Comprehensive Monitoring**
- **Real-time Progress**: Task state updates during execution
- **Health Checks**: Daily system health validation
- **Execution Reports**: Detailed JSON reports for each run
- **Performance Metrics**: Integration with existing `scraper_stats.json`

### 🔧 **Production Ready**
- **Docker Support**: Complete containerized deployment
- **Environment Configs**: Development, production, and testing setups
- **Log Management**: Automated cleanup of old logs
- **Resource Management**: Controlled memory and CPU usage

## 📋 Prerequisites

### System Requirements
- Python 3.11+
- Redis server
- PostgreSQL database (existing setup)
- Windows/Linux/macOS support

### Required Packages
```bash
pip install -r requirements-celery.txt
```

## 🛠️ Installation & Setup

### 1. **Install Dependencies**
```bash
# Install Celery system requirements
pip install -r requirements-celery.txt

# Install Redis (Windows)
# Download from: https://github.com/microsoftarchive/redis/releases
# Or use Docker: docker run -d -p 6379:6379 redis:alpine
```

### 2. **Environment Configuration**
Add to your `backend/.env` file:
```bash
# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0
ENVIRONMENT=production

# Optional: Monitoring and Alerting
MONITORING_ALERT_WEBHOOK_URL=https://your-webhook-url
```

### 3. **Verify Setup**
```bash
python manage_celery.py status
```

## 🚀 Usage

### **Quick Start**
```bash
# Start all services (worker + scheduler)
python manage_celery.py start-all

# Check status
python manage_celery.py status

# Run a test task
python manage_celery.py test-task
```

### **Individual Services**
```bash
# Start only worker
python manage_celery.py start-worker

# Start only scheduler
python manage_celery.py start-beat

# Stop all services
python manage_celery.py stop-all
```

### **Docker Deployment** (Recommended for Production)
```bash
# Start all services with Docker
docker-compose -f docker-compose.celery.yml up -d

# View logs
docker-compose -f docker-compose.celery.yml logs -f

# Stop services
docker-compose -f docker-compose.celery.yml down
```

### **Monitoring**
```bash
# Start Celery monitoring
python manage_celery.py monitor

# Web-based monitoring (with Docker)
docker-compose -f docker-compose.celery.yml --profile monitoring up -d
# Access at: http://localhost:5555
```

## ⚙️ Configuration

### **Scheduling Configuration**

#### Production (Default)
```python
'run-udemy-scraper-weekly': {
    'schedule': crontab(
        hour=2,        # 2 AM IST
        minute=0,      # At the top of the hour
        day_of_week=1  # Monday
    )
}
```

#### Development (More Frequent)
```python
'run-udemy-scraper-dev': {
    'schedule': crontab(minute='*/30')  # Every 30 minutes
}
```

### **Environment-Specific Settings**

Set `ENVIRONMENT` variable to switch configurations:
- `development`: Frequent testing schedules
- `production`: Weekly production schedule
- `testing`: Synchronous execution for tests

## 📊 Monitoring & Reporting

### **Execution Reports**
Each scraper run generates a detailed report:
```json
{
  "status": "SUCCESS",
  "task_id": "abc123...",
  "execution_time": 1234.56,
  "started_at": "2025-06-12T02:00:00",
  "completed_at_ist": "2025-06-12 02:20:34 IST",
  "scraper_stats": {
    "total_courses": 150,
    "processed": 145,
    "success_rate": 96.67,
    "last_scraping_completed": "2025-06-12 02:20:34 IST"
  }
}
```

### **Health Checks**
Daily health checks monitor:
- Database connectivity
- Scraper script availability
- Disk space
- Recent execution status

### **Log Files**
- `celery_scraper.log`: Main Celery operations
- `execution_report_*.json`: Individual run reports
- `health_report_*.json`: Daily health check results

## 🚨 Error Handling & Alerts

### **Automatic Retries**
- **3 retry attempts** with exponential backoff
- **5-minute base delay** between retries
- **Detailed error logging** for troubleshooting

### **Alert System**
The system can send alerts for:
- Scraper execution failures
- Health check failures
- Critical system errors

Configure webhook URL in environment:
```bash
MONITORING_ALERT_WEBHOOK_URL=https://hooks.slack.com/your-webhook
```

## 🔧 Troubleshooting

### **Common Issues**

1. **Redis Connection Failed**
   ```bash
   # Check Redis status
   redis-cli ping
   
   # Start Redis (Windows)
   redis-server
   
   # Or use Docker
   docker run -d -p 6379:6379 redis:alpine
   ```

2. **Worker Not Starting**
   ```bash
   # Check dependencies
   python manage_celery.py status
   
   # View detailed logs
   celery -A celery_app worker --loglevel=debug
   ```

3. **Database Connection Issues**
   ```bash
   # Test database connection
   python -c "from celery_app import validate_database_connection; validate_database_connection()"
   ```

### **Log Analysis**
```bash
# View recent execution reports
python manage_celery.py status

# Check Celery logs
tail -f celery_scraper.log

# Monitor task execution
celery -A celery_app events
```

## 🔄 Maintenance

### **Regular Tasks**
- **Log Cleanup**: Automated weekly cleanup of old logs
- **Health Monitoring**: Daily health checks
- **Performance Review**: Weekly execution reports

### **Manual Operations**
```bash
# Force immediate scraper run
python manage_celery.py test-task

# Clean up old files manually
find . -name "execution_report_*.json" -mtime +90 -delete

# Reset Celery beat schedule
rm -f celerybeat-schedule
```

## 🐳 Docker Production Deployment

### **Complete Stack**
```bash
# Start Redis, Worker, Beat, and Monitoring
docker-compose -f docker-compose.celery.yml --profile monitoring up -d

# Scale workers if needed
docker-compose -f docker-compose.celery.yml up -d --scale celery-worker=2
```

### **Health Checks**
```bash
# Check container health
docker-compose -f docker-compose.celery.yml ps

# View service logs
docker-compose -f docker-compose.celery.yml logs celery-worker
docker-compose -f docker-compose.celery.yml logs celery-beat
```

## 📈 Performance Optimization

### **Resource Tuning**
- **Worker Concurrency**: Adjust based on system resources
- **Task Time Limits**: Configure based on expected execution time
- **Memory Management**: Monitor and adjust container limits

### **Scaling**
- **Horizontal Scaling**: Add more worker containers
- **Load Balancing**: Distribute tasks across multiple workers
- **Database Optimization**: Connection pooling and query optimization

## 🔐 Security Considerations

- **Environment Variables**: Secure credential storage
- **Network Security**: Redis authentication and SSL
- **Container Security**: Non-root user execution
- **Log Security**: Sensitive data filtering

## 📞 Support

For issues or questions:
1. Check the status with `python manage_celery.py status`
2. Review execution reports and logs
3. Run health checks and diagnostics
4. Consult troubleshooting section

---

**Version**: 1.0.0  
**Last Updated**: 2025-06-12  
**Compatibility**: Python 3.11+, Celery 5.3+, Redis 5.0+
