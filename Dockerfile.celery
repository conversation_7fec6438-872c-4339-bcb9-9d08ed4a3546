# Dockerfile for Celery-based Udemy Scraper Automation
# ====================================================

FROM python:3.11-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV DEBIAN_FRONTEND=noninteractive

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    ca-certificates \
    fonts-liberation \
    libasound2 \
    libatk-bridge2.0-0 \
    libatk1.0-0 \
    libatspi2.0-0 \
    libcups2 \
    libdbus-1-3 \
    libdrm2 \
    libgtk-3-0 \
    libnspr4 \
    libnss3 \
    libwayland-client0 \
    libx11-6 \
    libx11-xcb1 \
    libxcb1 \
    libxcomposite1 \
    libxdamage1 \
    libxext6 \
    libxfixes3 \
    libxrandr2 \
    libxss1 \
    libxtst6 \
    xdg-utils \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements files
COPY requirements-celery.txt .
COPY backend/requirements.txt backend/requirements.txt

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements-celery.txt
RUN pip install --no-cache-dir -r backend/requirements.txt

# Install Playwright and browsers
RUN pip install playwright==1.40.0
RUN playwright install chromium
RUN playwright install-deps chromium

# Copy application files
COPY celery_app.py .
COPY celery_tasks.py .
COPY celery_config.py .
COPY manage_celery.py .
COPY backend/ backend/

# Create necessary directories
RUN mkdir -p /app/logs /app/data

# Set permissions
RUN chmod +x manage_celery.py

# Health check script
COPY <<EOF /app/healthcheck.py
#!/usr/bin/env python3
import sys
import subprocess
import json

def check_celery_worker():
    try:
        result = subprocess.run([
            'celery', '-A', 'celery_app', 'inspect', 'ping'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            return True
        else:
            print(f"Worker check failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"Health check error: {e}")
        return False

if __name__ == '__main__':
    if check_celery_worker():
        sys.exit(0)
    else:
        sys.exit(1)
EOF

RUN chmod +x /app/healthcheck.py

# Default command (can be overridden in docker-compose)
CMD ["celery", "-A", "celery_app", "worker", "--loglevel=info"]

# Labels for metadata
LABEL maintainer="Udemy Scraper Automation System"
LABEL version="1.0.0"
LABEL description="Celery-based automation for Udemy course scraping"
