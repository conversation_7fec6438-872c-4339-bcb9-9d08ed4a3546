# Enhanced Udemy Course Scraper - Deployment Guide

## Pre-Deployment Checklist

### 1. System Requirements
- **Operating System**: Linux (Ubuntu 20.04+ recommended), macOS, or Windows with WSL2
- **Memory**: Minimum 2GB RAM, 4GB+ recommended
- **Storage**: 10GB+ free space for logs and temporary files
- **Network**: Stable internet connection with good bandwidth
- **Docker**: Version 20.10+ (if using containerized deployment)

### 2. Database Requirements
- **PostgreSQL**: Version 12+ with pgvector extension
- **Database Size**: Ensure sufficient space for course catalog growth
- **Permissions**: Database user with CREATE, INSERT, UPDATE, SELECT permissions
- **Network**: Database accessible from scraper deployment location

### 3. Environment Preparation

```bash
# Clone the repository
git clone <your-repo-url>
cd mentoring-agent

# Verify all scraper files are present
ls -la backend/udemy_course_scraper_enhanced.py
ls -la scraper_config.py
ls -la scraper_monitor.py
ls -la run_scraper.sh
ls -la Dockerfile.scraper
ls -la docker-compose.yml
```

## Deployment Options

### Option 1: Docker Deployment (Recommended)

#### Step 1: Environment Configuration
```bash
# Copy environment template
cp backend/.env.example backend/.env

# Edit environment variables
nano backend/.env
```

Required environment variables:
```bash
# Database Configuration
DB_HOST=your_database_host
DB_PORT=5432
DB_USER=your_database_user
DB_PASSWORD=your_secure_password
DB_NAME=your_database_name

# Optional: Scraper Configuration
SCRAPER_MAX_CONCURRENT=5
SCRAPER_UPDATE_INTERVAL_DAYS=7
SCRAPER_REQUEST_TIMEOUT=15000

# Optional: Monitoring
MONITORING_ALERT_WEBHOOK_URL=https://your-webhook-url
LOG_LEVEL=INFO
```

#### Step 2: Build and Deploy
```bash
# Build the scraper container
docker-compose build udemy-scraper

# Start the scraper service
docker-compose up -d udemy-scraper

# Verify deployment
docker-compose ps
docker-compose logs udemy-scraper
```

#### Step 3: Verify Operation
```bash
# Check container health
docker-compose exec udemy-scraper python3 scraper_monitor.py

# View logs
docker-compose logs -f udemy-scraper

# Check stats file
docker-compose exec udemy-scraper cat scraper_stats.json
```

### Option 2: Manual Deployment

#### Step 1: Install Dependencies
```bash
# Install Python dependencies
pip install -r requirements-scraper.txt

# Install Playwright browsers
playwright install chromium
playwright install-deps chromium
```

#### Step 2: Database Setup
```bash
# Test database connection
python3 -c "
import asyncio
import asyncpg
import os
from dotenv import load_dotenv

load_dotenv()
async def test():
    conn = await asyncpg.connect(
        host=os.getenv('DB_HOST'),
        port=int(os.getenv('DB_PORT', 5432)),
        user=os.getenv('DB_USER'),
        password=os.getenv('DB_PASSWORD'),
        database=os.getenv('DB_NAME')
    )
    result = await conn.fetchval('SELECT 1')
    print(f'Database connection: {\"✅ Success\" if result == 1 else \"❌ Failed\"}')
    await conn.close()

asyncio.run(test())
"
```

#### Step 3: Run Tests
```bash
# Run comprehensive test suite
python3 test_enhanced_scraper.py
```

#### Step 4: Manual Execution
```bash
# Make script executable
chmod +x run_scraper.sh

# Run scraper manually
./run_scraper.sh

# Or run Python script directly
python3 backend/udemy_course_scraper_enhanced.py
```

#### Step 5: Setup Cron (for automation)
```bash
# Edit crontab
crontab -e

# Add scraper schedule (every 6 hours)
0 */6 * * * /path/to/your/project/run_scraper.sh >> /path/to/logs/cron.log 2>&1
```

## Post-Deployment Configuration

### 1. Monitoring Setup

```bash
# Create monitoring cron job
echo "0 */1 * * * python3 /path/to/scraper_monitor.py >> /path/to/logs/monitoring.log 2>&1" | crontab -
```

### 2. Log Rotation
```bash
# Create logrotate configuration
sudo nano /etc/logrotate.d/udemy-scraper

# Add configuration:
/path/to/logs/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 user group
}
```

### 3. Alerting Configuration

For Slack integration:
```bash
# Set webhook URL in environment
export MONITORING_ALERT_WEBHOOK_URL="https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK"
```

For email alerts, configure your system's mail service or use a service like SendGrid.

## Scaling Considerations

### Horizontal Scaling
```bash
# Run multiple instances with different course segments
# Instance 1: Courses 1-10000
export COURSE_ID_START=1
export COURSE_ID_END=10000

# Instance 2: Courses 10001-20000
export COURSE_ID_START=10001
export COURSE_ID_END=20000
```

### Vertical Scaling
```yaml
# Adjust Docker resource limits in docker-compose.yml
deploy:
  resources:
    limits:
      memory: 4G
      cpus: '2.0'
```

### Database Optimization
```sql
-- Create additional indexes for performance
CREATE INDEX CONCURRENTLY idx_udemy_course_catalog_composite 
ON udemy_course_catalog(course_language, is_it_course, last_updated);

-- Analyze table statistics
ANALYZE udemy_course_catalog;
```

## Troubleshooting

### Common Issues

1. **Container Won't Start**
   ```bash
   # Check logs
   docker-compose logs udemy-scraper
   
   # Check configuration
   docker-compose config
   ```

2. **Database Connection Issues**
   ```bash
   # Test connection from container
   docker-compose exec udemy-scraper python3 -c "
   import asyncio
   import asyncpg
   import os
   
   async def test():
       try:
           conn = await asyncpg.connect(
               host=os.getenv('DB_HOST'),
               port=int(os.getenv('DB_PORT', 5432)),
               user=os.getenv('DB_USER'),
               password=os.getenv('DB_PASSWORD'),
               database=os.getenv('DB_NAME')
           )
           print('✅ Database connection successful')
           await conn.close()
       except Exception as e:
           print(f'❌ Database connection failed: {e}')
   
   asyncio.run(test())
   "
   ```

3. **Browser Launch Failures**
   ```bash
   # Install missing dependencies
   docker-compose exec udemy-scraper playwright install-deps chromium
   ```

4. **Memory Issues**
   ```bash
   # Reduce concurrent browsers
   export SCRAPER_MAX_CONCURRENT=3
   
   # Restart container
   docker-compose restart udemy-scraper
   ```

### Performance Tuning

1. **Optimize Concurrency**
   ```bash
   # Monitor system resources
   docker stats udemy-scraper
   
   # Adjust based on available resources
   export SCRAPER_MAX_CONCURRENT=8  # For powerful systems
   export SCRAPER_MAX_CONCURRENT=3  # For limited resources
   ```

2. **Database Performance**
   ```sql
   -- Monitor database performance
   SELECT * FROM pg_stat_activity WHERE datname = 'your_database_name';
   
   -- Optimize connection pool
   export DB_MAX_POOL_SIZE=15
   export DB_MIN_POOL_SIZE=5
   ```

## Maintenance

### Regular Tasks

1. **Weekly**
   - Review scraper statistics and success rates
   - Check disk space usage
   - Verify database performance

2. **Monthly**
   - Update browser dependencies: `playwright install chromium`
   - Review and archive old logs
   - Update Python dependencies if needed

3. **Quarterly**
   - Review and optimize database indexes
   - Analyze scraping patterns and adjust scheduling
   - Update system dependencies

### Backup Strategy

```bash
# Database backup
pg_dump -h $DB_HOST -U $DB_USER -d $DB_NAME > backup_$(date +%Y%m%d).sql

# Configuration backup
tar -czf config_backup_$(date +%Y%m%d).tar.gz backend/.env scraper_config.py
```

## Security Considerations

1. **Environment Variables**: Never commit `.env` files to version control
2. **Database Access**: Use dedicated database user with minimal required permissions
3. **Network Security**: Consider VPN or private networks for database access
4. **Container Security**: Regularly update base images and dependencies
5. **Log Security**: Ensure logs don't contain sensitive information

## Support and Monitoring

### Health Checks
```bash
# Manual health check
python3 scraper_monitor.py

# Automated health check endpoint (if implemented)
curl http://localhost:8080/health
```

### Performance Monitoring
```bash
# View real-time stats
tail -f scraper_stats.json

# Generate monitoring report
python3 scraper_monitor.py > monitoring_report.json
```

For additional support, refer to the main SCRAPER_README.md file or check the troubleshooting section.
