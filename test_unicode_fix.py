#!/usr/bin/env python3
"""
Test script to verify Unicode fixes for Celery system
"""

import sys
import os
import platform

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_safe_logging():
    """Test the safe logging functionality."""
    print("=" * 60)
    print("Testing Unicode Fixes for Celery System")
    print("=" * 60)
    print(f"Platform: {platform.system()}")
    print(f"Python version: {sys.version}")
    print("=" * 60)
    
    try:
        # Test importing celery_app
        print("[TEST] Importing celery_app...")
        from celery_app import safe_log, get_ist_timestamp
        print("[SUCCESS] celery_app imported successfully")
        
        # Test safe_log function
        print("\n[TEST] Testing safe_log function...")
        test_messages = [
            ("Celery app configured", "🚀", "[CELERY]"),
            ("Scraper script path found", "📁", "[PATH]"),
            ("Timezone configured", "🕐", "[TIME]"),
            ("Weekly schedule set", "📅", "[SCHEDULE]"),
        ]
        
        for message, emoji, fallback in test_messages:
            safe_message = safe_log(message, emoji, fallback)
            print(f"  {safe_message}")
        
        print("[SUCCESS] safe_log function working correctly")
        
        # Test timestamp function
        print("\n[TEST] Testing IST timestamp...")
        timestamp = get_ist_timestamp()
        print(f"  Current IST time: {timestamp}")
        print("[SUCCESS] IST timestamp working correctly")
        
        # Test manage_celery functions
        print("\n[TEST] Testing manage_celery functions...")
        from manage_celery import safe_print, print_status
        
        print("  Testing safe_print...")
        safe_print("Test message", "✅", "[TEST]")
        
        print("  Testing print_status...")
        print_status("System status check", "SUCCESS")
        print_status("Warning message", "WARNING")
        print_status("Error message", "ERROR")
        
        print("[SUCCESS] manage_celery functions working correctly")
        
        print("\n" + "=" * 60)
        print("[OVERALL] All Unicode fixes are working correctly!")
        print("The Celery system should now work without Unicode errors.")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"[ERROR] Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_celery_import():
    """Test if Celery can be imported without Unicode errors."""
    print("\n[TEST] Testing Celery import...")
    
    try:
        # This should not cause Unicode errors anymore
        import celery_app
        print("[SUCCESS] Celery app imported without Unicode errors")
        return True
    except Exception as e:
        print(f"[ERROR] Celery import failed: {e}")
        return False

def main():
    """Main test function."""
    print("Starting Unicode Fix Verification Tests...")
    
    # Test 1: Safe logging functions
    test1_passed = test_safe_logging()
    
    # Test 2: Celery import
    test2_passed = test_celery_import()
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"Safe Logging Test: {'PASSED' if test1_passed else 'FAILED'}")
    print(f"Celery Import Test: {'PASSED' if test2_passed else 'FAILED'}")
    
    if test1_passed and test2_passed:
        print("\n[SUCCESS] All tests passed! Unicode issues are fixed.")
        print("You can now use the Celery system without Unicode errors.")
    else:
        print("\n[ERROR] Some tests failed. Please check the errors above.")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
