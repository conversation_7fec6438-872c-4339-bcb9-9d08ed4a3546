#!/usr/bin/env python3
"""
Test Suite for Enhanced Udemy Course Scraper
===========================================

This script provides comprehensive testing for the enhanced scraper system.

Author: Mentoring Agent Development Team
Version: 2.0.0
"""

import asyncio
import asyncpg
import logging
import sys
import os
from datetime import datetime, timedelta
from typing import Dict, Any

# Add backend directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from scraper_config import get_config, validate_config
from scraper_monitor import ScraperMonitor

# Setup logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

class ScraperTestSuite:
    """Comprehensive test suite for the enhanced scraper."""
    
    def __init__(self):
        self.config = get_config()
        self.test_results = {}
    
    def test_configuration(self) -> bool:
        """Test configuration validation."""
        logger.info("🧪 Testing configuration validation...")
        
        try:
            validate_config()
            logger.info("✅ Configuration validation passed")
            return True
        except ValueError as e:
            logger.error(f"❌ Configuration validation failed: {e}")
            return False
    
    async def test_database_connection(self) -> bool:
        """Test database connectivity."""
        logger.info("🧪 Testing database connection...")
        
        try:
            pool = await asyncpg.create_pool(**self.config.database.to_asyncpg_dict())
            
            async with pool.acquire() as conn:
                result = await conn.fetchval("SELECT 1")
                if result == 1:
                    logger.info("✅ Database connection successful")
                    
                    # Test table existence
                    table_exists = await conn.fetchval("""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables 
                            WHERE table_name = 'udemy_course_catalog'
                        )
                    """)
                    
                    if table_exists:
                        logger.info("✅ udemy_course_catalog table exists")
                    else:
                        logger.warning("⚠️ udemy_course_catalog table not found")
                        return False
                    
            await pool.close()
            return True
            
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            return False
    
    async def test_database_schema(self) -> bool:
        """Test database schema requirements."""
        logger.info("🧪 Testing database schema...")
        
        try:
            pool = await asyncpg.create_pool(**self.config.database.to_asyncpg_dict())
            
            async with pool.acquire() as conn:
                # Check required columns
                required_columns = [
                    'product_id', 'product_name', 'product_url', 'course_language',
                    'is_it_course', 'rating_value', 'rating_count', 'enrollments',
                    'video_content_length', 'last_updated'
                ]
                
                for column in required_columns:
                    exists = await conn.fetchval("""
                        SELECT EXISTS (
                            SELECT FROM information_schema.columns 
                            WHERE table_name = 'udemy_course_catalog' 
                            AND column_name = $1
                        )
                    """, column)
                    
                    if exists:
                        logger.info(f"✅ Column '{column}' exists")
                    else:
                        logger.warning(f"⚠️ Column '{column}' missing")
                        return False
                
                # Check indexes
                indexes = await conn.fetch("""
                    SELECT indexname FROM pg_indexes 
                    WHERE tablename = 'udemy_course_catalog'
                """)
                
                index_names = [idx['indexname'] for idx in indexes]
                logger.info(f"📊 Found indexes: {index_names}")
                
            await pool.close()
            return True
            
        except Exception as e:
            logger.error(f"❌ Database schema test failed: {e}")
            return False
    
    async def test_sample_data(self) -> bool:
        """Test sample data availability."""
        logger.info("🧪 Testing sample data...")
        
        try:
            pool = await asyncpg.create_pool(**self.config.database.to_asyncpg_dict())
            
            async with pool.acquire() as conn:
                # Count total courses
                total_courses = await conn.fetchval("SELECT COUNT(*) FROM udemy_course_catalog")
                logger.info(f"📊 Total courses in database: {total_courses}")
                
                # Count IT courses
                it_courses = await conn.fetchval(
                    "SELECT COUNT(*) FROM udemy_course_catalog WHERE is_it_course = TRUE"
                )
                logger.info(f"📊 IT courses: {it_courses}")
                
                # Count English courses
                english_courses = await conn.fetchval(
                    "SELECT COUNT(*) FROM udemy_course_catalog WHERE course_language = 'en'"
                )
                logger.info(f"📊 English courses: {english_courses}")
                
                # Count courses needing update
                courses_needing_update = await conn.fetchval(f"""
                    SELECT COUNT(*) FROM udemy_course_catalog 
                    WHERE course_language = 'en' 
                        AND is_it_course = TRUE
                        AND (last_updated IS NULL OR last_updated < CURRENT_TIMESTAMP - INTERVAL '{self.config.scraper.update_interval_days} days')
                """)
                logger.info(f"📊 Courses needing update: {courses_needing_update}")
                
                if total_courses > 0:
                    logger.info("✅ Sample data available")
                    return True
                else:
                    logger.warning("⚠️ No courses found in database")
                    return False
                    
            await pool.close()
            
        except Exception as e:
            logger.error(f"❌ Sample data test failed: {e}")
            return False
    
    async def test_monitoring_system(self) -> bool:
        """Test monitoring system functionality."""
        logger.info("🧪 Testing monitoring system...")
        
        try:
            monitor = ScraperMonitor()
            
            # Test database metrics collection
            db_metrics = await monitor.get_database_metrics()
            if db_metrics:
                logger.info("✅ Database metrics collection successful")
                logger.info(f"📊 Metrics: {db_metrics.total_courses} total courses, {db_metrics.courses_needing_update} need updates")
            else:
                logger.error("❌ Database metrics collection failed")
                return False
            
            # Test health check
            health_status = await monitor.check_scraper_health()
            if health_status:
                logger.info(f"✅ Health check successful: {health_status['overall_status']}")
            else:
                logger.error("❌ Health check failed")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Monitoring system test failed: {e}")
            return False
    
    def test_file_permissions(self) -> bool:
        """Test file permissions and accessibility."""
        logger.info("🧪 Testing file permissions...")
        
        files_to_check = [
            'backend/udemy_course_scraper_enhanced.py',
            'scraper_config.py',
            'scraper_monitor.py',
            'run_scraper.sh'
        ]
        
        all_good = True
        for file_path in files_to_check:
            if os.path.exists(file_path):
                if os.access(file_path, os.R_OK):
                    logger.info(f"✅ {file_path} is readable")
                else:
                    logger.error(f"❌ {file_path} is not readable")
                    all_good = False
                    
                if file_path.endswith('.sh'):
                    if os.access(file_path, os.X_OK):
                        logger.info(f"✅ {file_path} is executable")
                    else:
                        logger.warning(f"⚠️ {file_path} is not executable")
            else:
                logger.error(f"❌ {file_path} not found")
                all_good = False
        
        return all_good
    
    def test_environment_variables(self) -> bool:
        """Test environment variables."""
        logger.info("🧪 Testing environment variables...")
        
        required_vars = ['DB_HOST', 'DB_USER', 'DB_PASSWORD', 'DB_NAME']
        all_good = True
        
        for var in required_vars:
            value = os.getenv(var)
            if value:
                logger.info(f"✅ {var} is set")
            else:
                logger.error(f"❌ {var} is not set")
                all_good = False
        
        return all_good
    
    async def run_all_tests(self) -> Dict[str, bool]:
        """Run all tests and return results."""
        logger.info("🚀 Starting Enhanced Scraper Test Suite")
        logger.info("=" * 60)
        
        tests = [
            ("Configuration Validation", self.test_configuration),
            ("Environment Variables", self.test_environment_variables),
            ("File Permissions", self.test_file_permissions),
            ("Database Connection", self.test_database_connection),
            ("Database Schema", self.test_database_schema),
            ("Sample Data", self.test_sample_data),
            ("Monitoring System", self.test_monitoring_system),
        ]
        
        results = {}
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            logger.info(f"\n--- {test_name} ---")
            try:
                if asyncio.iscoroutinefunction(test_func):
                    result = await test_func()
                else:
                    result = test_func()
                
                results[test_name] = result
                if result:
                    passed += 1
                    
            except Exception as e:
                logger.error(f"❌ {test_name} failed with exception: {e}")
                results[test_name] = False
        
        # Summary
        logger.info("\n" + "=" * 60)
        logger.info("🏁 Test Suite Summary")
        logger.info("=" * 60)
        
        for test_name, result in results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            logger.info(f"{status} - {test_name}")
        
        logger.info(f"\nOverall: {passed}/{total} tests passed")
        
        if passed == total:
            logger.info("🎉 All tests passed! The enhanced scraper is ready for deployment.")
        else:
            logger.warning(f"⚠️ {total - passed} test(s) failed. Please address the issues before deployment.")
        
        return results

async def main():
    """Main test function."""
    test_suite = ScraperTestSuite()
    results = await test_suite.run_all_tests()
    
    # Exit with appropriate code
    all_passed = all(results.values())
    sys.exit(0 if all_passed else 1)

if __name__ == "__main__":
    asyncio.run(main())
