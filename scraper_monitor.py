#!/usr/bin/env python3
"""
Enhanced Udemy Course Scraper Monitoring System
==============================================

This module provides monitoring, alerting, and health check capabilities
for the enhanced scraper system.

Author: Mentoring Agent Development Team
Version: 2.0.0
"""

import os
import json
import time
import asyncio
import logging
import asyncpg
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from pathlib import Path
import httpx

from scraper_config import get_config

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

@dataclass
class ScraperMetrics:
    """Metrics collected from scraper execution."""
    timestamp: str
    total_courses: int
    processed: int
    skipped: int
    errors: int
    failed: int
    success_rate: float
    courses_per_hour: float
    elapsed_time: float
    
    @classmethod
    def from_stats_file(cls, file_path: str) -> Optional['ScraperMetrics']:
        """Load metrics from stats file."""
        try:
            if not os.path.exists(file_path):
                return None
            
            with open(file_path, 'r') as f:
                data = json.load(f)
            
            return cls(
                timestamp=datetime.now().isoformat(),
                total_courses=data.get('total_courses', 0),
                processed=data.get('processed', 0),
                skipped=data.get('skipped', 0),
                errors=data.get('errors', 0),
                failed=data.get('failed', 0),
                success_rate=data.get('success_rate', 0.0),
                courses_per_hour=data.get('courses_per_hour', 0.0),
                elapsed_time=data.get('elapsed_time', 0.0)
            )
        except Exception as e:
            logger.error(f"Error loading metrics from {file_path}: {e}")
            return None

@dataclass
class DatabaseMetrics:
    """Database-related metrics."""
    total_courses: int
    it_courses: int
    english_courses: int
    courses_with_ratings: int
    courses_with_enrollments: int
    courses_updated_today: int
    courses_updated_this_week: int
    courses_needing_update: int
    avg_rating: float
    total_enrollments: int

class ScraperMonitor:
    """Main monitoring class for the scraper system."""
    
    def __init__(self):
        self.config = get_config()
        self.metrics_history: List[ScraperMetrics] = []
        
    async def get_database_metrics(self) -> Optional[DatabaseMetrics]:
        """Collect metrics from the database."""
        try:
            pool = await asyncpg.create_pool(**self.config.database.to_asyncpg_dict())
            
            async with pool.acquire() as conn:
                # Total courses
                total_courses = await conn.fetchval("SELECT COUNT(*) FROM udemy_course_catalog")
                
                # IT courses
                it_courses = await conn.fetchval(
                    "SELECT COUNT(*) FROM udemy_course_catalog WHERE is_it_course = TRUE"
                )
                
                # English courses
                english_courses = await conn.fetchval(
                    "SELECT COUNT(*) FROM udemy_course_catalog WHERE course_language = 'en'"
                )
                
                # Courses with ratings
                courses_with_ratings = await conn.fetchval(
                    "SELECT COUNT(*) FROM udemy_course_catalog WHERE rating_value IS NOT NULL AND rating_value != 'N/A'"
                )
                
                # Courses with enrollments
                courses_with_enrollments = await conn.fetchval(
                    "SELECT COUNT(*) FROM udemy_course_catalog WHERE enrollments IS NOT NULL AND enrollments != 'N/A'"
                )
                
                # Courses updated today
                courses_updated_today = await conn.fetchval(
                    "SELECT COUNT(*) FROM udemy_course_catalog WHERE DATE(last_updated) = CURRENT_DATE"
                )
                
                # Courses updated this week
                courses_updated_this_week = await conn.fetchval(
                    "SELECT COUNT(*) FROM udemy_course_catalog WHERE last_updated >= CURRENT_DATE - INTERVAL '7 days'"
                )
                
                # Courses needing update
                courses_needing_update = await conn.fetchval(f"""
                    SELECT COUNT(*) FROM udemy_course_catalog 
                    WHERE course_language = 'en' 
                        AND is_it_course = TRUE
                        AND (last_updated IS NULL OR last_updated < CURRENT_TIMESTAMP - INTERVAL '{self.config.scraper.update_interval_days} days')
                """)
                
                # Average rating
                avg_rating_result = await conn.fetchval(
                    "SELECT AVG(CAST(rating_value AS FLOAT)) FROM udemy_course_catalog WHERE rating_value IS NOT NULL AND rating_value != 'N/A'"
                )
                avg_rating = float(avg_rating_result) if avg_rating_result else 0.0
                
                # Total enrollments
                total_enrollments_result = await conn.fetchval(
                    "SELECT SUM(CAST(enrollments AS BIGINT)) FROM udemy_course_catalog WHERE enrollments IS NOT NULL AND enrollments != 'N/A'"
                )
                total_enrollments = int(total_enrollments_result) if total_enrollments_result else 0
            
            await pool.close()
            
            return DatabaseMetrics(
                total_courses=total_courses,
                it_courses=it_courses,
                english_courses=english_courses,
                courses_with_ratings=courses_with_ratings,
                courses_with_enrollments=courses_with_enrollments,
                courses_updated_today=courses_updated_today,
                courses_updated_this_week=courses_updated_this_week,
                courses_needing_update=courses_needing_update,
                avg_rating=round(avg_rating, 2),
                total_enrollments=total_enrollments
            )
            
        except Exception as e:
            logger.error(f"Error collecting database metrics: {e}")
            return None
    
    def load_recent_metrics(self, hours: int = 24) -> List[ScraperMetrics]:
        """Load recent metrics from history."""
        # In a production system, this would load from a persistent store
        # For now, we'll just return the current metrics if available
        stats_file = self.config.monitoring.stats_file_path
        current_metrics = ScraperMetrics.from_stats_file(stats_file)
        
        if current_metrics:
            return [current_metrics]
        return []
    
    async def check_scraper_health(self) -> Dict[str, Any]:
        """Perform comprehensive health check of the scraper system."""
        health_status = {
            "timestamp": datetime.now().isoformat(),
            "overall_status": "healthy",
            "checks": {}
        }
        
        # Check database connectivity
        try:
            db_metrics = await self.get_database_metrics()
            if db_metrics:
                health_status["checks"]["database"] = {
                    "status": "healthy",
                    "total_courses": db_metrics.total_courses,
                    "courses_needing_update": db_metrics.courses_needing_update
                }
            else:
                health_status["checks"]["database"] = {"status": "unhealthy", "error": "Could not collect metrics"}
                health_status["overall_status"] = "unhealthy"
        except Exception as e:
            health_status["checks"]["database"] = {"status": "unhealthy", "error": str(e)}
            health_status["overall_status"] = "unhealthy"
        
        # Check recent scraper execution
        stats_file = self.config.monitoring.stats_file_path
        if os.path.exists(stats_file):
            file_age = time.time() - os.path.getmtime(stats_file)
            hours_since_update = file_age / 3600
            
            if hours_since_update < 24:  # Stats file updated within 24 hours
                recent_metrics = ScraperMetrics.from_stats_file(stats_file)
                if recent_metrics and recent_metrics.success_rate >= 80:
                    health_status["checks"]["scraper_execution"] = {
                        "status": "healthy",
                        "last_run_hours_ago": round(hours_since_update, 1),
                        "success_rate": recent_metrics.success_rate
                    }
                else:
                    health_status["checks"]["scraper_execution"] = {
                        "status": "warning",
                        "last_run_hours_ago": round(hours_since_update, 1),
                        "success_rate": recent_metrics.success_rate if recent_metrics else 0,
                        "issue": "Low success rate"
                    }
                    health_status["overall_status"] = "warning"
            else:
                health_status["checks"]["scraper_execution"] = {
                    "status": "warning",
                    "last_run_hours_ago": round(hours_since_update, 1),
                    "issue": "No recent execution"
                }
                health_status["overall_status"] = "warning"
        else:
            health_status["checks"]["scraper_execution"] = {
                "status": "unhealthy",
                "issue": "No stats file found"
            }
            health_status["overall_status"] = "unhealthy"
        
        # Check disk space
        try:
            import shutil
            total, used, free = shutil.disk_usage(".")
            free_gb = free // (1024**3)
            
            if free_gb > 5:  # More than 5GB free
                health_status["checks"]["disk_space"] = {
                    "status": "healthy",
                    "free_gb": free_gb
                }
            elif free_gb > 1:  # 1-5GB free
                health_status["checks"]["disk_space"] = {
                    "status": "warning",
                    "free_gb": free_gb,
                    "issue": "Low disk space"
                }
                health_status["overall_status"] = "warning"
            else:  # Less than 1GB free
                health_status["checks"]["disk_space"] = {
                    "status": "unhealthy",
                    "free_gb": free_gb,
                    "issue": "Very low disk space"
                }
                health_status["overall_status"] = "unhealthy"
        except Exception as e:
            health_status["checks"]["disk_space"] = {"status": "unknown", "error": str(e)}
        
        return health_status
    
    async def send_alert(self, message: str, severity: str = "warning"):
        """Send alert notification."""
        if not self.config.monitoring.alert_webhook_url:
            logger.warning(f"Alert would be sent: [{severity.upper()}] {message}")
            return
        
        try:
            async with httpx.AsyncClient() as client:
                payload = {
                    "text": f"🚨 Udemy Scraper Alert [{severity.upper()}]: {message}",
                    "timestamp": datetime.now().isoformat()
                }
                
                response = await client.post(
                    self.config.monitoring.alert_webhook_url,
                    json=payload,
                    timeout=10.0
                )
                response.raise_for_status()
                logger.info(f"Alert sent successfully: {message}")
        except Exception as e:
            logger.error(f"Failed to send alert: {e}")
    
    async def generate_report(self) -> Dict[str, Any]:
        """Generate comprehensive monitoring report."""
        logger.info("Generating monitoring report...")
        
        # Collect all metrics
        db_metrics = await self.get_database_metrics()
        health_status = await self.check_scraper_health()
        recent_metrics = self.load_recent_metrics()
        
        report = {
            "generated_at": datetime.now().isoformat(),
            "health_status": health_status,
            "database_metrics": asdict(db_metrics) if db_metrics else None,
            "recent_executions": [asdict(m) for m in recent_metrics],
            "configuration": self.config.to_dict()
        }
        
        # Check for alerts
        if health_status["overall_status"] != "healthy":
            await self.send_alert(f"System health check failed: {health_status['overall_status']}")
        
        if db_metrics and db_metrics.courses_needing_update > 1000:
            await self.send_alert(f"Large number of courses need updating: {db_metrics.courses_needing_update}")
        
        return report

async def main():
    """Main monitoring function."""
    monitor = ScraperMonitor()
    
    # Generate and display report
    report = await monitor.generate_report()
    
    print("Enhanced Udemy Course Scraper Monitoring Report")
    print("=" * 60)
    print(json.dumps(report, indent=2))
    
    # Save report to file
    report_file = f"scraper_monitoring_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\nReport saved to: {report_file}")

if __name__ == "__main__":
    asyncio.run(main())
