#!/usr/bin/env python3
"""
Force Update Test Script
========================

This script temporarily resets the last_updated timestamp for a few courses
to demonstrate the scraper in action.

Usage:
    python force_update_test.py [--count N]
"""

import os
import sys
import asyncio
import asyncpg
import argparse
from datetime import datetime
from dotenv import load_dotenv

load_dotenv('backend/.env')

async def reset_course_timestamps(count=5):
    """Reset last_updated for a few courses to force updates."""
    
    try:
        # Connect to database
        conn = await asyncpg.connect(
            host=os.getenv('DB_HOST'),
            port=os.getenv('DB_PORT'),
            user=os.getenv('DB_USER'),
            password=os.getenv('DB_PASSWORD'),
            database=os.getenv('DB_NAME')
        )
        
        print(f"🔄 Resetting last_updated for {count} courses...")
        
        # Get some courses to reset
        courses = await conn.fetch("""
            SELECT product_id, product_name 
            FROM udemy_course_catalog 
            WHERE course_language = 'en' 
                AND is_it_course = TRUE 
                AND product_url IS NOT NULL
                AND last_updated IS NOT NULL
            ORDER BY RANDOM()
            LIMIT $1
        """, count)
        
        if not courses:
            print("❌ No suitable courses found to reset")
            await conn.close()
            return False
        
        # Reset their timestamps
        course_ids = [course['product_id'] for course in courses]
        
        result = await conn.execute("""
            UPDATE udemy_course_catalog 
            SET last_updated = CURRENT_TIMESTAMP - INTERVAL '10 days'
            WHERE product_id = ANY($1)
        """, course_ids)
        
        await conn.close()
        
        print(f"✅ Successfully reset {len(courses)} courses:")
        for course in courses:
            print(f"   📚 {course['product_name'][:60]}...")
        
        print(f"\n🎯 These courses will now be picked up by the scraper!")
        return True
        
    except Exception as e:
        print(f"❌ Error resetting timestamps: {e}")
        return False

async def check_courses_needing_update():
    """Check how many courses need updating."""
    try:
        conn = await asyncpg.connect(
            host=os.getenv('DB_HOST'),
            port=os.getenv('DB_PORT'),
            user=os.getenv('DB_USER'),
            password=os.getenv('DB_PASSWORD'),
            database=os.getenv('DB_NAME')
        )
        
        count = await conn.fetchval("""
            SELECT COUNT(*) FROM udemy_course_catalog
            WHERE course_language = 'en'
                AND is_it_course = TRUE
                AND product_url IS NOT NULL
                AND (
                    last_updated IS NULL
                    OR last_updated < (CURRENT_TIMESTAMP - INTERVAL '7 days')
                )
        """)
        
        await conn.close()
        return count
        
    except Exception as e:
        print(f"❌ Error checking courses: {e}")
        return 0

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Force update test for scraper")
    parser.add_argument("--count", type=int, default=5,
                       help="Number of courses to reset (default: 5)")
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("🧪 FORCE UPDATE TEST SCRIPT")
    print("=" * 60)
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # Check current status
    print("📊 Checking current status...")
    current_count = asyncio.run(check_courses_needing_update())
    print(f"   Currently {current_count} courses need updating")
    
    if current_count > 0:
        print("✅ Courses already need updating - no need to force")
        return
    
    # Reset some courses
    success = asyncio.run(reset_course_timestamps(args.count))
    
    if success:
        # Check again
        new_count = asyncio.run(check_courses_needing_update())
        print(f"\n📊 After reset: {new_count} courses now need updating")
        print("\n🚀 You can now run the scraper to see it in action:")
        print("   python monitor_scraper.py")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
