#!/usr/bin/env python3
"""
Enhanced Udemy Course Scraper Configuration Management
=====================================================

This module provides centralized configuration management for the scraper
with environment variable support, validation, and default values.

Author: Mentoring Agent Development Team
Version: 2.0.0
"""

import os
from typing import Dict, Any, Optional
from dataclasses import dataclass
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

@dataclass
class DatabaseConfig:
    """Database connection configuration."""
    host: str
    port: int
    user: str
    password: str
    database: str
    min_pool_size: int = 2
    max_pool_size: int = 10
    command_timeout: int = 60
    
    @classmethod
    def from_env(cls) -> 'DatabaseConfig':
        """Create database config from environment variables."""
        return cls(
            host=os.getenv("DB_HOST", "localhost"),
            port=int(os.getenv("DB_PORT", "5432")),
            user=os.getenv("DB_USER", ""),
            password=os.getenv("DB_PASSWORD", ""),
            database=os.getenv("DB_NAME", ""),
            min_pool_size=int(os.getenv("DB_MIN_POOL_SIZE", "2")),
            max_pool_size=int(os.getenv("DB_MAX_POOL_SIZE", "10")),
            command_timeout=int(os.getenv("DB_COMMAND_TIMEOUT", "60"))
        )
    
    def validate(self) -> bool:
        """Validate that all required fields are set."""
        required_fields = [self.host, self.user, self.password, self.database]
        return all(field.strip() for field in required_fields)
    
    def to_asyncpg_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format for asyncpg connection."""
        return {
            "host": self.host,
            "port": self.port,
            "user": self.user,
            "password": self.password,
            "database": self.database
        }

@dataclass
class ScraperConfig:
    """Scraper behavior configuration."""
    max_concurrent_browsers: int = 5
    update_interval_days: int = 7
    request_timeout: int = 15000
    max_retries: int = 3
    retry_delay_base: float = 1.5
    user_agent: str = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    
    @classmethod
    def from_env(cls) -> 'ScraperConfig':
        """Create scraper config from environment variables."""
        return cls(
            max_concurrent_browsers=int(os.getenv("SCRAPER_MAX_CONCURRENT", "5")),
            update_interval_days=int(os.getenv("SCRAPER_UPDATE_INTERVAL_DAYS", "7")),
            request_timeout=int(os.getenv("SCRAPER_REQUEST_TIMEOUT", "15000")),
            max_retries=int(os.getenv("SCRAPER_MAX_RETRIES", "3")),
            retry_delay_base=float(os.getenv("SCRAPER_RETRY_DELAY_BASE", "1.5")),
            user_agent=os.getenv("SCRAPER_USER_AGENT", cls.user_agent)
        )
    
    def validate(self) -> bool:
        """Validate configuration values."""
        return (
            1 <= self.max_concurrent_browsers <= 20 and
            1 <= self.update_interval_days <= 365 and
            5000 <= self.request_timeout <= 60000 and
            1 <= self.max_retries <= 10 and
            0.5 <= self.retry_delay_base <= 5.0
        )

@dataclass
class LoggingConfig:
    """Logging configuration."""
    level: str = "INFO"
    format: str = "%(asctime)s - %(levelname)s - [%(funcName)s:%(lineno)d] - %(message)s"
    file_path: str = "udemy_scraper_enhanced.log"
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5
    
    @classmethod
    def from_env(cls) -> 'LoggingConfig':
        """Create logging config from environment variables."""
        return cls(
            level=os.getenv("LOG_LEVEL", "INFO").upper(),
            format=os.getenv("LOG_FORMAT", cls.format),
            file_path=os.getenv("LOG_FILE_PATH", cls.file_path),
            max_file_size=int(os.getenv("LOG_MAX_FILE_SIZE", str(cls.max_file_size))),
            backup_count=int(os.getenv("LOG_BACKUP_COUNT", str(cls.backup_count)))
        )

@dataclass
class MonitoringConfig:
    """Monitoring and alerting configuration."""
    enable_stats_file: bool = True
    stats_file_path: str = "scraper_stats.json"
    enable_health_check: bool = True
    health_check_port: int = 8080
    alert_on_failure_rate: float = 0.1  # Alert if failure rate > 10%
    alert_webhook_url: Optional[str] = None
    
    @classmethod
    def from_env(cls) -> 'MonitoringConfig':
        """Create monitoring config from environment variables."""
        return cls(
            enable_stats_file=os.getenv("MONITORING_ENABLE_STATS", "true").lower() == "true",
            stats_file_path=os.getenv("MONITORING_STATS_FILE", cls.stats_file_path),
            enable_health_check=os.getenv("MONITORING_ENABLE_HEALTH_CHECK", "true").lower() == "true",
            health_check_port=int(os.getenv("MONITORING_HEALTH_CHECK_PORT", str(cls.health_check_port))),
            alert_on_failure_rate=float(os.getenv("MONITORING_ALERT_FAILURE_RATE", str(cls.alert_on_failure_rate))),
            alert_webhook_url=os.getenv("MONITORING_ALERT_WEBHOOK_URL")
        )

class EnhancedScraperConfig:
    """Main configuration class that combines all configuration sections."""
    
    def __init__(self):
        self.database = DatabaseConfig.from_env()
        self.scraper = ScraperConfig.from_env()
        self.logging = LoggingConfig.from_env()
        self.monitoring = MonitoringConfig.from_env()
    
    def validate(self) -> tuple[bool, list[str]]:
        """
        Validate all configuration sections.
        
        Returns:
            tuple: (is_valid, list_of_errors)
        """
        errors = []
        
        if not self.database.validate():
            errors.append("Database configuration is incomplete. Check DB_HOST, DB_USER, DB_PASSWORD, DB_NAME.")
        
        if not self.scraper.validate():
            errors.append("Scraper configuration has invalid values. Check ranges for concurrent browsers, timeouts, etc.")
        
        # Additional validation
        if self.scraper.max_concurrent_browsers > 10:
            errors.append("Warning: High concurrent browser count may cause resource issues.")
        
        if self.scraper.update_interval_days < 1:
            errors.append("Update interval must be at least 1 day.")
        
        return len(errors) == 0, errors
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary for logging/debugging."""
        return {
            "database": {
                "host": self.database.host,
                "port": self.database.port,
                "user": self.database.user,
                "database": self.database.database,
                "pool_size": f"{self.database.min_pool_size}-{self.database.max_pool_size}"
            },
            "scraper": {
                "max_concurrent": self.scraper.max_concurrent_browsers,
                "update_interval_days": self.scraper.update_interval_days,
                "request_timeout": self.scraper.request_timeout,
                "max_retries": self.scraper.max_retries,
                "retry_delay_base": self.scraper.retry_delay_base
            },
            "logging": {
                "level": self.logging.level,
                "file_path": self.logging.file_path
            },
            "monitoring": {
                "stats_enabled": self.monitoring.enable_stats_file,
                "health_check_enabled": self.monitoring.enable_health_check,
                "alert_failure_rate": self.monitoring.alert_on_failure_rate
            }
        }

# Global configuration instance
config = EnhancedScraperConfig()

def get_config() -> EnhancedScraperConfig:
    """Get the global configuration instance."""
    return config

def validate_config() -> None:
    """Validate configuration and raise exception if invalid."""
    is_valid, errors = config.validate()
    if not is_valid:
        error_message = "Configuration validation failed:\n" + "\n".join(f"- {error}" for error in errors)
        raise ValueError(error_message)

if __name__ == "__main__":
    # Configuration validation script
    print("Enhanced Udemy Course Scraper Configuration")
    print("=" * 50)
    
    try:
        validate_config()
        print("✅ Configuration is valid!")
        
        print("\nCurrent Configuration:")
        import json
        print(json.dumps(config.to_dict(), indent=2))
        
    except ValueError as e:
        print(f"❌ Configuration validation failed:")
        print(e)
        exit(1)
