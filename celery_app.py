#!/usr/bin/env python3
"""
Celery Application Configuration for Udemy Course Scraper
=========================================================

This module configures Celery for automated scheduling of the Udemy course scraper.
Supports both development and production environments with proper error handling,
monitoring, and IST timezone configuration.

Usage:
    # Start Celery worker
    celery -A celery_app worker --loglevel=info

    # Start Celery Beat scheduler
    celery -A celery_app beat --loglevel=info

    # Start both worker and beat (development)
    celery -A celery_app worker --beat --loglevel=info
"""

import os
import sys
import logging
from datetime import datetime, timezone, timedelta
from celery import Celery
from celery.schedules import crontab
from dotenv import load_dotenv

# Load environment variables
load_dotenv('backend/.env')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('celery_scraper.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# IST Timezone configuration
IST_TIMEZONE = timezone(timedelta(hours=5, minutes=30))

# Celery configuration
CELERY_BROKER_URL = os.getenv('CELERY_BROKER_URL', 'redis://localhost:6379/0')
CELERY_RESULT_BACKEND = os.getenv('CELERY_RESULT_BACKEND', 'redis://localhost:6379/0')

# Environment detection
ENVIRONMENT = os.getenv('ENVIRONMENT', 'development')
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))
SCRAPER_SCRIPT_PATH = os.path.join(PROJECT_ROOT, 'backend', 'udemy_course_scraper_enhanced.py')

# Validate scraper script exists
if not os.path.exists(SCRAPER_SCRIPT_PATH):
    logger.error(f"Scraper script not found at: {SCRAPER_SCRIPT_PATH}")
    sys.exit(1)

# Create Celery application
app = Celery('udemy_scraper_scheduler')

# Celery configuration
app.conf.update(
    broker_url=CELERY_BROKER_URL,
    result_backend=CELERY_RESULT_BACKEND,
    timezone='Asia/Kolkata',  # IST timezone
    enable_utc=True,
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    task_track_started=True,
    task_time_limit=7200,  # 2 hours max execution time
    task_soft_time_limit=6600,  # 1 hour 50 minutes soft limit
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    worker_disable_rate_limits=False,
    task_default_retry_delay=300,  # 5 minutes
    task_max_retries=3,
    
    # Beat schedule configuration
    beat_schedule={
        'run-udemy-scraper-weekly': {
            'task': 'celery_app.run_udemy_scraper',
            'schedule': crontab(
                hour=2,        # 2 AM IST
                minute=0,      # At the top of the hour
                day_of_week=1  # Monday (0=Sunday, 1=Monday, etc.)
            ),
            'options': {
                'expires': 3600,  # Task expires after 1 hour if not picked up
            }
        },
        
        # Health check task (daily)
        'health-check-daily': {
            'task': 'celery_app.health_check',
            'schedule': crontab(hour=1, minute=0),  # 1 AM IST daily
            'options': {
                'expires': 300,  # 5 minutes
            }
        },
        
        # Cleanup old logs (weekly)
        'cleanup-logs-weekly': {
            'task': 'celery_app.cleanup_old_logs',
            'schedule': crontab(
                hour=3,        # 3 AM IST
                minute=0,
                day_of_week=0  # Sunday
            ),
            'options': {
                'expires': 1800,  # 30 minutes
            }
        }
    },
    beat_schedule_filename='celerybeat-schedule',
)

# Configure logging for Celery
@app.task(bind=True)
def setup_logging(self):
    """Configure logging for Celery tasks."""
    task_logger = logging.getLogger(f'celery.task.{self.name}')
    task_logger.setLevel(logging.INFO)
    
    # Create file handler for task-specific logs
    handler = logging.FileHandler(f'celery_task_{self.name}.log')
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - [%(task_id)s] - %(message)s'
    )
    handler.setFormatter(formatter)
    task_logger.addHandler(handler)
    
    return task_logger

def get_ist_timestamp():
    """Get current timestamp in IST format."""
    now_ist = datetime.now(IST_TIMEZONE)
    return now_ist.strftime("%Y-%m-%d %H:%M:%S IST")

def validate_database_connection():
    """Validate database connection before running scraper."""
    try:
        import asyncio
        import asyncpg
        
        async def test_connection():
            conn = await asyncpg.connect(
                host=os.getenv('DB_HOST'),
                port=os.getenv('DB_PORT'),
                user=os.getenv('DB_USER'),
                password=os.getenv('DB_PASSWORD'),
                database=os.getenv('DB_NAME')
            )
            
            # Test query
            result = await conn.fetchval('SELECT COUNT(*) FROM udemy_course_catalog')
            await conn.close()
            return result
        
        course_count = asyncio.run(test_connection())
        logger.info(f"✅ Database connection validated. Found {course_count:,} courses in catalog")
        return True
        
    except Exception as e:
        logger.error(f"❌ Database connection failed: {e}")
        return False

# Import tasks after app configuration
try:
    if __name__ != '__main__':
        from celery_tasks import *
except ImportError:
    logger.warning("celery_tasks module not found - tasks will be loaded when available")

logger.info(f"🚀 Celery app configured for {ENVIRONMENT} environment")
logger.info(f"📁 Scraper script path: {SCRAPER_SCRIPT_PATH}")
logger.info(f"🕐 Timezone: Asia/Kolkata (IST)")
logger.info(f"📅 Weekly schedule: Every Monday at 2:00 AM IST")
