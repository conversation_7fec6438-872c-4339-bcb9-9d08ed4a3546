version: '3.8'

services:
  # Enhanced Udemy Course Scraper Service
  udemy-scraper:
    build:
      context: .
      dockerfile: Dockerfile.scraper
    container_name: udemy-course-scraper
    restart: unless-stopped
    
    # Environment variables (override with .env file)
    environment:
      - DB_HOST=${DB_HOST:-localhost}
      - DB_PORT=${DB_PORT:-5432}
      - DB_USER=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_NAME=${DB_NAME}
      - SCRAPER_MAX_CONCURRENT=${SCRAPER_MAX_CONCURRENT:-5}
      - SCRAPER_UPDATE_INTERVAL_DAYS=${SCRAPER_UPDATE_INTERVAL_DAYS:-7}
      - SCRAPER_REQUEST_TIMEOUT=${SCRAPER_REQUEST_TIMEOUT:-15000}
      - SCRAPER_MAX_RETRIES=${SCRAPER_MAX_RETRIES:-3}
      - SCRA<PERSON>ER_RETRY_DELAY_BASE=${SCRAPER_RETRY_DELAY_BASE:-1.5}
    
    # Volume mounts for persistence
    volumes:
      - ./logs:/app/logs
      - ./scraper_stats.json:/app/scraper_stats.json
      - ./backend/.env:/app/.env:ro
    
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    
    # Health check
    healthcheck:
      test: ["CMD", "python3", "-c", "import os; exit(0 if os.path.exists('/app/scraper_stats.json') else 1)"]
      interval: 30m
      timeout: 10s
      retries: 3
      start_period: 5m
    
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"
    
    # Network configuration
    networks:
      - mentoring-agent-network
    
    # Dependencies (if database is also in Docker)
    # depends_on:
    #   - postgres

  # Optional: PostgreSQL Database Service
  # Uncomment if you want to run the database in Docker as well
  # postgres:
  #   image: postgres:15-alpine
  #   container_name: mentoring-agent-db
  #   restart: unless-stopped
  #   
  #   environment:
  #     - POSTGRES_DB=${DB_NAME}
  #     - POSTGRES_USER=${DB_USER}
  #     - POSTGRES_PASSWORD=${DB_PASSWORD}
  #     - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
  #   
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #     - ./init.sql:/docker-entrypoint-initdb.d/init.sql:ro
  #   
  #   ports:
  #     - "${DB_PORT:-5432}:5432"
  #   
  #   networks:
  #     - mentoring-agent-network
  #   
  #   healthcheck:
  #     test: ["CMD-SHELL", "pg_isready -U ${DB_USER} -d ${DB_NAME}"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 5

  # Optional: Monitoring Service (Prometheus/Grafana)
  # monitoring:
  #   image: prom/prometheus:latest
  #   container_name: scraper-monitoring
  #   restart: unless-stopped
  #   
  #   volumes:
  #     - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
  #     - prometheus_data:/prometheus
  #   
  #   ports:
  #     - "9090:9090"
  #   
  #   networks:
  #     - mentoring-agent-network

# Networks
networks:
  mentoring-agent-network:
    driver: bridge
    name: mentoring-agent-network

# Volumes (uncomment if using PostgreSQL in Docker)
# volumes:
#   postgres_data:
#     driver: local
#   prometheus_data:
#     driver: local
