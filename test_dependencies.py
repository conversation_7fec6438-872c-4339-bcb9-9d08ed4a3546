#!/usr/bin/env python3
"""
Test script to verify dependency check fix
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_dependency_check():
    """Test the fixed dependency check function."""
    print("=" * 60)
    print("Testing Dependency Check Fix")
    print("=" * 60)
    
    try:
        from manage_celery import check_dependencies
        
        print("[TEST] Running dependency check...")
        result = check_dependencies()
        
        if result:
            print("[SUCCESS] All dependencies are correctly detected!")
        else:
            print("[ERROR] Dependency check failed")
        
        return result
        
    except Exception as e:
        print(f"[ERROR] Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_individual_imports():
    """Test importing each package individually."""
    print("\n[TEST] Testing individual package imports...")
    
    packages = {
        'celery': 'celery',
        'redis': 'redis',
        'asyncpg': 'asyncpg', 
        'python-dotenv': 'dotenv'
    }
    
    all_passed = True
    
    for pip_name, module_name in packages.items():
        try:
            __import__(module_name)
            print(f"  [SUCCESS] {pip_name} -> {module_name}")
        except ImportError as e:
            print(f"  [ERROR] {pip_name} -> {module_name}: {e}")
            all_passed = False
    
    return all_passed

def main():
    """Main test function."""
    print("Testing Dependency Check Fix...")
    
    # Test individual imports
    test1_passed = test_individual_imports()
    
    # Test dependency check function
    test2_passed = test_dependency_check()
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"Individual Imports: {'PASSED' if test1_passed else 'FAILED'}")
    print(f"Dependency Check: {'PASSED' if test2_passed else 'FAILED'}")
    
    if test1_passed and test2_passed:
        print("\n[SUCCESS] Dependency check is working correctly!")
        print("You can now use 'python manage_celery.py start-all' without errors.")
    else:
        print("\n[ERROR] Some tests failed. Please check missing packages.")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
