#!/bin/bash

# Enhanced Udemy Course Scraper Execution Script
# This script handles the execution of the scraper with proper logging and error handling

set -e  # Exit on any error

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_DIR="${SCRIPT_DIR}/logs"
SCRAPER_SCRIPT="${SCRIPT_DIR}/udemy_course_scraper_enhanced.py"
LOCK_FILE="${SCRIPT_DIR}/scraper.lock"
MAX_EXECUTION_TIME=7200  # 2 hours maximum execution time

# Ensure logs directory exists
mkdir -p "$LOG_DIR"

# Function to log messages with timestamp
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_DIR/scraper_execution.log"
}

# Function to cleanup on exit
cleanup() {
    local exit_code=$?
    log_message "Cleaning up..."
    
    # Remove lock file
    if [ -f "$LOCK_FILE" ]; then
        rm -f "$LOCK_FILE"
        log_message "Lock file removed"
    fi
    
    # Kill any remaining Python processes if they exist
    pkill -f "udemy_course_scraper_enhanced.py" 2>/dev/null || true
    
    if [ $exit_code -eq 0 ]; then
        log_message "Scraper execution completed successfully"
    else
        log_message "Scraper execution failed with exit code: $exit_code"
    fi
    
    exit $exit_code
}

# Set up trap for cleanup
trap cleanup EXIT INT TERM

# Check if scraper is already running
if [ -f "$LOCK_FILE" ]; then
    LOCK_PID=$(cat "$LOCK_FILE")
    if ps -p "$LOCK_PID" > /dev/null 2>&1; then
        log_message "Scraper is already running (PID: $LOCK_PID). Exiting."
        exit 1
    else
        log_message "Stale lock file found. Removing..."
        rm -f "$LOCK_FILE"
    fi
fi

# Create lock file
echo $$ > "$LOCK_FILE"
log_message "Created lock file with PID: $$"

# Check if scraper script exists
if [ ! -f "$SCRAPER_SCRIPT" ]; then
    log_message "ERROR: Scraper script not found at $SCRAPER_SCRIPT"
    exit 1
fi

# Check if .env file exists
if [ ! -f "${SCRIPT_DIR}/.env" ]; then
    log_message "WARNING: .env file not found. Make sure environment variables are set."
fi

# Log system information
log_message "=== Enhanced Udemy Course Scraper Execution Started ==="
log_message "Script directory: $SCRIPT_DIR"
log_message "Python version: $(python3 --version)"
log_message "Available memory: $(free -h | grep '^Mem:' | awk '{print $7}')"
log_message "Available disk space: $(df -h . | tail -1 | awk '{print $4}')"

# Change to script directory
cd "$SCRIPT_DIR"

# Execute the scraper with timeout
log_message "Starting scraper execution..."
timeout "$MAX_EXECUTION_TIME" python3 "$SCRAPER_SCRIPT" 2>&1 | tee -a "$LOG_DIR/scraper_output.log"

SCRAPER_EXIT_CODE=${PIPESTATUS[0]}

# Check execution result
if [ $SCRAPER_EXIT_CODE -eq 0 ]; then
    log_message "✅ Scraper completed successfully"
    
    # Archive old logs (keep last 30 days)
    find "$LOG_DIR" -name "*.log" -type f -mtime +30 -delete 2>/dev/null || true
    
    # Check if stats file was created
    if [ -f "${SCRIPT_DIR}/scraper_stats.json" ]; then
        log_message "📊 Statistics file created successfully"
        # Optionally, you could send stats to a monitoring system here
    fi
    
elif [ $SCRAPER_EXIT_CODE -eq 124 ]; then
    log_message "⏰ Scraper execution timed out after ${MAX_EXECUTION_TIME} seconds"
    exit 124
else
    log_message "❌ Scraper failed with exit code: $SCRAPER_EXIT_CODE"
    exit $SCRAPER_EXIT_CODE
fi

log_message "=== Scraper Execution Completed ==="
