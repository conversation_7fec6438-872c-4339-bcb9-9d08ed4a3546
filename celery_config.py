#!/usr/bin/env python3
"""
Celery Configuration for Different Environments
===============================================

This module provides environment-specific configurations for the Celery
scraper automation system.
"""

import os
from celery.schedules import crontab

class BaseConfig:
    """Base configuration for all environments."""
    
    # Celery settings
    broker_url = os.getenv('CELERY_BROKER_URL', 'redis://localhost:6379/0')
    result_backend = os.getenv('CELERY_RESULT_BACKEND', 'redis://localhost:6379/0')
    timezone = 'Asia/Kolkata'
    enable_utc = True
    
    # Task settings
    task_serializer = 'json'
    accept_content = ['json']
    result_serializer = 'json'
    task_track_started = True
    task_acks_late = True
    worker_prefetch_multiplier = 1
    
    # Retry settings
    task_default_retry_delay = 300  # 5 minutes
    task_max_retries = 3
    
    # Time limits
    task_time_limit = 7200  # 2 hours
    task_soft_time_limit = 6600  # 1 hour 50 minutes
    
    # Logging
    worker_log_format = '[%(asctime)s: %(levelname)s/%(processName)s] %(message)s'
    worker_task_log_format = '[%(asctime)s: %(levelname)s/%(processName)s][%(task_name)s(%(task_id)s)] %(message)s'

class DevelopmentConfig(BaseConfig):
    """Development environment configuration."""
    
    # More frequent scheduling for testing
    beat_schedule = {
        'run-udemy-scraper-dev': {
            'task': 'celery_app.run_udemy_scraper',
            'schedule': crontab(minute='*/30'),  # Every 30 minutes for testing
            'options': {'expires': 1800},
        },
        'health-check-dev': {
            'task': 'celery_app.health_check',
            'schedule': crontab(minute='*/15'),  # Every 15 minutes
            'options': {'expires': 300},
        }
    }
    
    # Development-specific settings
    task_always_eager = False  # Set to True for synchronous testing
    task_eager_propagates = True
    worker_log_level = 'DEBUG'

class ProductionConfig(BaseConfig):
    """Production environment configuration."""
    
    # Production scheduling - every 7 days
    beat_schedule = {
        'run-udemy-scraper-weekly': {
            'task': 'celery_app.run_udemy_scraper',
            'schedule': crontab(
                hour=2,        # 2 AM IST
                minute=0,      # At the top of the hour
                day_of_week=1  # Monday
            ),
            'options': {'expires': 3600},
        },
        
        'health-check-daily': {
            'task': 'celery_app.health_check',
            'schedule': crontab(hour=1, minute=0),  # 1 AM IST daily
            'options': {'expires': 300},
        },
        
        'cleanup-logs-weekly': {
            'task': 'celery_app.cleanup_old_logs',
            'schedule': crontab(
                hour=3,        # 3 AM IST
                minute=0,
                day_of_week=0  # Sunday
            ),
            'options': {'expires': 1800},
        }
    }
    
    # Production-specific settings
    worker_log_level = 'INFO'
    task_compression = 'gzip'
    result_compression = 'gzip'
    
    # Enhanced monitoring
    worker_send_task_events = True
    task_send_sent_event = True

class TestingConfig(BaseConfig):
    """Testing environment configuration."""
    
    # No scheduled tasks for testing
    beat_schedule = {}
    
    # Testing-specific settings
    task_always_eager = True  # Execute tasks synchronously
    task_eager_propagates = True
    broker_url = 'memory://'
    result_backend = 'cache+memory://'

# Configuration mapping
config_map = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
}

def get_config():
    """Get configuration based on environment."""
    env = os.getenv('ENVIRONMENT', 'development').lower()
    return config_map.get(env, DevelopmentConfig)

# Export current configuration
Config = get_config()
